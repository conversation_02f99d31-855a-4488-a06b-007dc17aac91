import katex from 'katex';
import 'katex/dist/katex.min.css';

/**
 * 渲染LaTeX公式
 * @param text 包含LaTeX公式的文本
 * @returns 渲染后的HTML字符串
 */
export function renderLatex(text: string): string {
  if (!text) return '';
  
  // 使用正则表达式匹配LaTeX公式（$...$ 格式）
  return text.replace(/\$(.*?)\$/g, (match, formula) => {
    try {
      // 使用KaTeX渲染公式
      return katex.renderToString(formula, {
        throwOnError: false, // 渲染错误时不抛出异常
        displayMode: false,  // 行内模式
        output: 'html',     // 输出HTML
        strict: false       // 宽松模式，允许部分语法错误
      });
    } catch (error) {
      console.error('LaTeX rendering error:', error);
      return match; // 渲染失败时返回原始文本
    }
  });
}