"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.audioDurationUtils = void 0;
const fs = require("fs/promises");
const path = require("path");
/**
 * 获取音频文件时长的工具函数
 */
exports.audioDurationUtils = {
    /**
     * 使用FFprobe精准获取音频时长
     * 这是最准确的方法，直接读取音频文件的元数据
     */
    async getMP3Duration(filePath) {
        try {
            const absolutePath = path.isAbsolute(filePath)
                ? filePath
                : path.join(process.cwd(), filePath);
            // 检查文件是否存在
            try {
                await fs.access(absolutePath);
            }
            catch {
                console.error(`[AudioDuration] 文件不存在: ${absolutePath}`);
                throw new Error(`音频文件不存在: ${filePath}`);
            }
            // 方案1：尝试使用node-ffprobe（如果已安装）
            try {
                const ffprobe = await Promise.resolve().then(() => require('node-ffprobe'));
                const probe = await ffprobe.default(absolutePath);
                if (probe.format && probe.format.duration) {
                    const duration = parseFloat(probe.format.duration);
                    const roundedDuration = Math.round(duration * 100) / 100; // 保留两位小数
                    console.log(`[AudioDuration] FFprobe获取音频时长: ${filePath} -> ${roundedDuration}秒`);
                    return roundedDuration;
                }
            }
            catch (ffprobeError) {
                console.warn(`[AudioDuration] FFprobe不可用，尝试其他方法: ${ffprobeError.message}`);
            }
            // 方案2：使用mp3-duration库（如果已安装）
            try {
                const mp3Duration = await Promise.resolve().then(() => require('mp3-duration'));
                const duration = await mp3Duration.default(absolutePath);
                const roundedDuration = Math.round(duration * 100) / 100;
                console.log(`[AudioDuration] mp3-duration获取音频时长: ${filePath} -> ${roundedDuration}秒`);
                return roundedDuration;
            }
            catch (mp3Error) {
                console.warn(`[AudioDuration] mp3-duration不可用，尝试其他方法: ${mp3Error.message}`);
            }
            // 方案3：手动解析MP3头部信息（精准方法）
            const duration = await this.parseMP3Header(absolutePath);
            const roundedDuration = Math.round(duration * 100) / 100;
            console.log(`[AudioDuration] 手动解析获取音频时长: ${filePath} -> ${roundedDuration}秒`);
            return roundedDuration;
        }
        catch (error) {
            console.error(`[AudioDuration] 获取音频时长失败: ${filePath}`, error);
            throw error;
        }
    },
    /**
     * 手动解析MP3文件头部信息获取精准时长
     */
    async parseMP3Header(filePath) {
        const buffer = await fs.readFile(filePath);
        // 查找第一个MP3帧头
        let frameStart = -1;
        for (let i = 0; i < buffer.length - 4; i++) {
            if (buffer[i] === 0xFF && (buffer[i + 1] & 0xE0) === 0xE0) {
                frameStart = i;
                break;
            }
        }
        if (frameStart === -1) {
            throw new Error('无法找到MP3帧头');
        }
        // 解析MP3帧头
        const header = buffer.readUInt32BE(frameStart);
        // 提取版本、层、比特率、采样率等信息
        const version = (header >> 19) & 0x3;
        const layer = (header >> 17) & 0x3;
        const bitrateIndex = (header >> 12) & 0xF;
        const sampleRateIndex = (header >> 10) & 0x3;
        // MP3比特率表 (kbps)
        const bitrates = {
            1: {
                1: [0, 32, 64, 96, 128, 160, 192, 224, 256, 288, 320, 352, 384, 416, 448], // Layer I
                2: [0, 32, 48, 56, 64, 80, 96, 112, 128, 160, 192, 224, 256, 320, 384], // Layer II
                3: [0, 32, 40, 48, 56, 64, 80, 96, 112, 128, 160, 192, 224, 256, 320] // Layer III
            },
            2: {
                1: [0, 32, 48, 56, 64, 80, 96, 112, 128, 144, 160, 176, 192, 224, 256],
                2: [0, 8, 16, 24, 32, 40, 48, 56, 64, 80, 96, 112, 128, 144, 160],
                3: [0, 8, 16, 24, 32, 40, 48, 56, 64, 80, 96, 112, 128, 144, 160]
            }
        };
        // 采样率表 (Hz)
        const sampleRates = {
            1: [44100, 48000, 32000], // MPEG-1
            2: [22050, 24000, 16000], // MPEG-2
            3: [11025, 12000, 8000] // MPEG-2.5
        };
        const mpegVersion = version === 3 ? 1 : (version === 2 ? 2 : 3);
        const layerNum = 4 - layer;
        if (!bitrates[mpegVersion] || !bitrates[mpegVersion][layerNum]) {
            throw new Error('不支持的MP3格式');
        }
        const bitrate = bitrates[mpegVersion][layerNum][bitrateIndex];
        const sampleRate = sampleRates[mpegVersion][sampleRateIndex];
        if (!bitrate || !sampleRate) {
            throw new Error('无法解析MP3参数');
        }
        // 计算时长：文件大小 / (比特率 / 8)
        const fileSize = buffer.length;
        const duration = (fileSize * 8) / (bitrate * 1000);
        console.log(`[AudioDuration] MP3解析结果: 比特率=${bitrate}kbps, 采样率=${sampleRate}Hz, 文件大小=${(fileSize / 1024).toFixed(1)}KB`);
        return duration;
    },
    /**
     * 使用Web Audio API获取音频时长（在有浏览器环境时使用）
     * 这个方法更准确，但只能在浏览器环境中使用
     */
    async getAudioDurationFromURL(url) {
        return new Promise((resolve, reject) => {
            const audio = new Audio();
            audio.addEventListener('loadedmetadata', () => {
                resolve(audio.duration);
            });
            audio.addEventListener('error', (error) => {
                console.error(`[AudioDuration] 加载音频元数据失败: ${url}`, error);
                reject(new Error(`无法加载音频元数据: ${url}`));
            });
            // 设置超时
            setTimeout(() => {
                console.error(`[AudioDuration] 获取音频时长超时: ${url}`);
                reject(new Error(`获取音频时长超时: ${url}`));
            }, 10000); // 10秒超时
            audio.src = url;
        });
    },
    /**
     * 批量获取音频时长
     */
    async getBatchDurations(filePaths) {
        const results = {};
        for (const filePath of filePaths) {
            try {
                results[filePath] = await this.getMP3Duration(filePath);
            }
            catch (error) {
                console.error(`[AudioDuration] 批量处理失败: ${filePath}`, error);
                // 不再使用默认值，记录错误但继续处理其他文件
                results[filePath] = null;
            }
        }
        return results;
    }
};
