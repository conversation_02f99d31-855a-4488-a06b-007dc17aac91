import { audioUtils } from './audio';

export class AudioProcessor {
  static async processTaskAudio(taskId: string, stemId: number, rawResponse: string, useProcessing: boolean = true) {
    console.log('\n[AudioProcessor] Starting audio processing');
    console.log('Task ID:', taskId);
    console.log('Stem ID:', stemId);
    console.log('Use Processing:', useProcessing);

    try {
      // 处理音频URL并更新new_response
      await audioUtils.processAudioUrls(rawResponse, taskId, useProcessing);
      console.log('[AudioProcessor] Audio processing completed');
    } catch (error) {
      console.error('[AudioProcessor] Error processing audio:', error);
      throw error;
    }
  }
}