const Database = require('better-sqlite3');
const path = require('path');

// 定义表记录的类型接口
interface TableRecord {
  name: string;
}

interface SchemaRecord {
  sql: string;
}

// 连接数据库
const db = new Database(path.join(process.cwd(), 'local.db'));

// 获取所有表名
const tables = db.prepare(`
  SELECT name FROM sqlite_master 
  WHERE type='table' 
  ORDER BY name;
`).all() as TableRecord[];

// 遍历每个表并获取其结构
tables.forEach(table => {
  console.log(`\n=== Table: ${table.name} ===`);
  
  const schema = db.prepare(`
    SELECT sql FROM sqlite_master 
    WHERE type='table' AND name = ?;
  `).get(table.name) as SchemaRecord;
  
  // 格式化输出
  const formattedSql = schema.sql
    .replace(/CREATE TABLE/i, 'CREATE TABLE')
    .replace(/\(/g, '\n  (')
    .replace(/,/g, ',\n   ')
    .replace(/\);/g, '\n  );');
    
  console.log(formattedSql);
});

// 关闭数据库连接
db.close();