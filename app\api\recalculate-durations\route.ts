import { NextResponse } from 'next/server';
import { recalculateAudioDurations } from '@/scripts/recalculate-audio-durations';

export async function POST(request: Request) {
  try {
    console.log('[API] 开始重新计算音频时长');
    
    // 执行重新计算
    await recalculateAudioDurations();
    
    return NextResponse.json({
      success: true,
      message: '音频时长重新计算完成'
    });
    
  } catch (error) {
    console.error('[API] 重新计算音频时长失败:', error);
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
