'use client';

import { BookOpenIcon, ChartBarIcon, LightBulbIcon, DocumentTextIcon } from '@heroicons/react/24/solid';
import WavyUnderline from './WavyUnderline';
import CircleMarker from './CircleMarker';
import StraightUnderline from './StraightUnderline';
import { useRef, useEffect, useState } from 'react';
import katex from 'katex';
import 'katex/dist/katex.min.css';

const katexOverrideStyles = `
  .katex {
    font: inherit !important;
    line-height: inherit !important;
  }
  .katex .katex-html {
    white-space: normal !important;
  }
  .katex .base {
    margin: 0 !important;
    padding: 0 !important;
  }
  .hide-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
`;

interface QuestionContentProps {
  data: {
    content: string;
    stem_txt?: string;
    analysis: string;
    answer: string;
    summary: string;
    new_response?: string | null;
    currentAudioFile?: string;
    audioFiles?: Array<{
      url: string;
      filename: string;
      duration?: number;
    }>;
  };
  isAnimation?: boolean;
}

interface ParsedContent {
  stem: {
    url: string;
    yw?: string;
    zm?: string;  // 字幕
    fl?: string;  // 分类
    mark?: Array<{text: string; type: 'circle' | 'wave'}>; // 标记
  }[];
  analysis: {
    url: string;
    yw?: string;
    zm?: string;  // 字幕
    fl?: string;  // 分类
  }[];
  answer: {
    url: string;
    yw?: string;
    zm?: string;  // 字幕
    fl?: string;  // 分类
  }[];
  summary: {
    url: string;
    yw?: string;
    zm?: string;  // 字幕
    fl?: string;  // 分类
  }[];
}

interface TextVisibility {
  [key: string]: boolean; // 音频文件名到可见性的映射
}

interface TextPosition {
  text: string;
  type?: 'circle' | 'wave' | 'straight'; // 标记类型，添加直线类型
  textIndex?: number; // 文本在原文中的位置
  filename?: string; // 所属文件名
  lines: {
    start: number;    // x坐标
    end: number;      // x坐标
    top: number;      // y坐标
    baseline: number; // 基线位置
  }[];
}

interface ProcessedText {
  type: 'text' | 'math' | 'highlight' | 'bold';
  content: string;
}

// 添加文本处理函数
const processText = (text: string | undefined): ProcessedText[] => {
  // 如果文本为空，返回空数组
  if (!text) return [];
  
  const parts: ProcessedText[] = [];
  let currentIndex = 0;

  // 正则表达式匹配所有特殊标记
  const pattern = /(\⟦.*?\⟧)|(\*\*.*?\*\*)|(\\[\[].*?\\[\]])|(\$\$.*?\$\$)|(\$.*?\$)/g;
  let match;

  while ((match = pattern.exec(text)) !== null) {
    // 添加匹配前的普通文本
    if (match.index > currentIndex) {
      parts.push({
        type: 'text',
        content: text.slice(currentIndex, match.index)
      });
    }

    // 处理匹配的特殊标记
    const [fullMatch] = match;
    if (fullMatch.startsWith('⟦')) {
      // 高亮文本
      parts.push({
        type: 'highlight',
        content: fullMatch.slice(1, -1)
      });
    } else if (fullMatch.startsWith('**')) {
      // 加粗文本
      parts.push({
        type: 'bold',
        content: fullMatch.slice(2, -2)
      });
    } else if (fullMatch.startsWith('\\[')) {
      // LaTeX 公式 \[...\]
      parts.push({
        type: 'math',
        content: fullMatch.slice(2, -2)
      });
    } else if (fullMatch.startsWith('$$')) {
      // LaTeX 公式 $$...$$
      parts.push({
        type: 'math',
        content: fullMatch.slice(2, -2)
      });
    } else if (fullMatch.startsWith('$')) {
      // LaTeX 公式 $...$
      parts.push({
        type: 'math',
        content: fullMatch.slice(1, -1)
      });
    }

    currentIndex = match.index + fullMatch.length;
  }

  // 添加剩余的普通文本
  if (currentIndex < text.length) {
    parts.push({
      type: 'text',
      content: text.slice(currentIndex)
    });
  }

  return parts;
};

// 渲染处理后的文本
const renderProcessedText = (text: string | undefined, isTypewriter: boolean = false) => {
  if (!text) return null;

  const parts = processText(text);

  return (
    <>
      {parts.map((part, index) => {
        switch (part.type) {
          case 'highlight':
            return (
              <span key={index} className="text-[#F57D23]">
                {part.content}
              </span>
            );
          case 'bold':
            return (
              <span key={index} className="font-bold">
                {part.content}
              </span>
            );
          case 'math':
            return (
              <span
                key={index}
                className={`text-[#F57D23] inline-flex items-center ${isTypewriter ? 'math-fade-in' : ''}`}
                dangerouslySetInnerHTML={{
                  __html: katex.renderToString(part.content, {
                    throwOnError: false,
                    displayMode: false,
                    output: 'html',
                    trust: true,
                    strict: false,
                    macros: {
                      '\\equal': '='
                    },
                    maxSize: 10,
                    minRuleThickness: 0.04,
                    maxExpand: 1000
                  })
                }}
                style={{
                  transform: 'translateY(0.5px)',
                }}
              />
            );
          default:
            return <span key={index}>{part.content}</span>;
        }
      })}
    </>
  );
};

export default function QuestionContent({ data, isAnimation = false }: QuestionContentProps) {
  const textRef = useRef<HTMLDivElement>(null);
  const [underlineWidth, setUnderlineWidth] = useState(0);
  const [parsedContent, setParsedContent] = useState<ParsedContent | null>(null);
  const [textVisibility, setTextVisibility] = useState<TextVisibility>({});
  const [textPositions, setTextPositions] = useState<{[key: string]: TextPosition}>({});
  const [staticTextPositions, setStaticTextPositions] = useState<{[key: string]: TextPosition}>({});
  const stemTextRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const [userScrolled, setUserScrolled] = useState(false);
  const [isAutoScrolling, setIsAutoScrolling] = useState(false);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [markAnimationTiming, setMarkAnimationTiming] = useState<{[key: string]: {delay: number, duration: number}}>({});
  const [completedMarks, setCompletedMarks] = useState<Set<string>>(new Set());
  const [markRenderStates, setMarkRenderStates] = useState<{[key: string]: 'hidden' | 'animating' | 'completed'}>({});
  const [currentSection, setCurrentSection] = useState<string>('');
  const [currentSubtitle, setCurrentSubtitle] = useState<string>('');
  const [typewriterText, setTypewriterText] = useState<{[key: string]: string}>({});
  const [typewriterCursor, setTypewriterCursor] = useState<{[key: string]: boolean}>({});
  const typewriterTimers = useRef<{[key: string]: NodeJS.Timeout}>({});
  const cursorTimers = useRef<{[key: string]: NodeJS.Timeout}>({});

  // 解析文本中的LaTeX公式和普通文本
  const parseTextForTypewriter = (text: string) => {
    const parts: Array<{type: 'text' | 'math', content: string}> = [];
    let currentIndex = 0;

    // 匹配LaTeX公式的正则表达式
    const mathPattern = /(\$\$.*?\$\$)|(\$.*?\$)|(\\[\[].*?\\[\]])/g;
    let match;

    while ((match = mathPattern.exec(text)) !== null) {
      // 添加公式前的普通文本
      if (match.index > currentIndex) {
        parts.push({
          type: 'text',
          content: text.slice(currentIndex, match.index)
        });
      }

      // 添加LaTeX公式
      parts.push({
        type: 'math',
        content: match[0]
      });

      currentIndex = match.index + match[0].length;
    }

    // 添加剩余的普通文本
    if (currentIndex < text.length) {
      parts.push({
        type: 'text',
        content: text.slice(currentIndex)
      });
    }

    return parts;
  };

  // 获取字符的打字延迟（添加自然的变化）
  const getCharacterDelay = (char: string, baseDelay: number, nextChar?: string): number => {
    // 句号、问号、感叹号后更长停顿
    if (/[。！？]/.test(char)) {
      return baseDelay * 4;
    }
    // 逗号、分号、冒号后中等停顿
    if (/[，；：]/.test(char)) {
      return baseDelay * 2.5;
    }
    // 顿号、括号后短停顿
    if (/[、（）【】《》""'']/.test(char)) {
      return baseDelay * 1.8;
    }
    // 空格稍作停顿
    if (char === ' ') {
      return baseDelay * 1.3;
    }
    // 换行符后稍作停顿
    if (char === '\n') {
      return baseDelay * 2;
    }
    // 数字和字母组合时稍快
    if (/[0-9a-zA-Z]/.test(char) && nextChar && /[0-9a-zA-Z]/.test(nextChar)) {
      return baseDelay * 0.7;
    }
    // 普通字符添加轻微随机性（±25%）
    const randomFactor = 0.75 + Math.random() * 0.5;
    return baseDelay * randomFactor;
  };

  // 启动光标闪烁效果
  const startCursor = (filename: string) => {
    setTypewriterCursor(prev => ({ ...prev, [filename]: true }));

    const blinkCursor = () => {
      setTypewriterCursor(prev => ({ ...prev, [filename]: !prev[filename] }));
      cursorTimers.current[filename] = setTimeout(blinkCursor, 500);
    };

    cursorTimers.current[filename] = setTimeout(blinkCursor, 500);
  };

  // 停止光标闪烁
  const stopCursor = (filename: string) => {
    if (cursorTimers.current[filename]) {
      clearTimeout(cursorTimers.current[filename]);
    }
    setTypewriterCursor(prev => ({ ...prev, [filename]: false }));
  };

  // 优化的打字机效果函数
  const startTypewriter = (filename: string, text: string, duration: number) => {
    console.log(`⌨️ [打字机] 开始打字机效果: ${filename}`, { text: text.substring(0, 50) + '...', duration });

    // 清除之前的定时器
    if (typewriterTimers.current[filename]) {
      clearTimeout(typewriterTimers.current[filename]);
    }
    stopCursor(filename);

    // 重置文本并启动光标
    setTypewriterText(prev => ({ ...prev, [filename]: '' }));
    startCursor(filename);

    // 解析文本为部分（普通文本和LaTeX公式）
    const textParts = parseTextForTypewriter(text);

    // 计算总的"单位"数（普通文本按字符计算，LaTeX公式按1个单位计算）
    const totalUnits = textParts.reduce((sum, part) => {
      return sum + (part.type === 'text' ? part.content.length : 1);
    }, 0);

    // 计算基础打字速度：在音频时长的45%内完成
    const targetDuration = duration * 0.45 * 1000; // 转换为毫秒
    const baseIntervalTime = targetDuration / totalUnits;

    let currentPartIndex = 0;
    let currentCharIndex = 0;
    let displayedText = '';

    const typeNext = () => {
      if (currentPartIndex >= textParts.length) {
        // 打字完成，停止光标
        stopCursor(filename);
        console.log(`⌨️ [打字机] 打字完成: ${filename}`);
        return;
      }

      const currentPart = textParts[currentPartIndex];

      if (currentPart.type === 'math') {
        // LaTeX公式作为整体添加，带有渐入效果
        displayedText += currentPart.content;
        currentPartIndex++;
        currentCharIndex = 0;

        // LaTeX公式显示时间稍长
        const mathDelay = baseIntervalTime * 2;

        setTypewriterText(prev => ({
          ...prev,
          [filename]: displayedText
        }));

        typewriterTimers.current[filename] = setTimeout(typeNext, mathDelay);
      } else {
        // 普通文本逐字符添加
        if (currentCharIndex < currentPart.content.length) {
          const currentChar = currentPart.content[currentCharIndex];
          const nextChar = currentCharIndex + 1 < currentPart.content.length
            ? currentPart.content[currentCharIndex + 1]
            : undefined;

          displayedText += currentChar;
          currentCharIndex++;

          setTypewriterText(prev => ({
            ...prev,
            [filename]: displayedText
          }));

          // 根据字符类型和下一个字符计算延迟
          const charDelay = getCharacterDelay(currentChar, baseIntervalTime, nextChar);
          typewriterTimers.current[filename] = setTimeout(typeNext, charDelay);
        } else {
          // 当前部分完成，移动到下一部分
          currentPartIndex++;
          currentCharIndex = 0;
          typeNext(); // 立即处理下一部分
        }
      }
    };

    // 开始打字
    typeNext();
  };

  // 解析 new_response
  useEffect(() => {
    if (data.new_response) {
      try {
        const content = JSON.parse(data.new_response) as ParsedContent;
        setParsedContent(content);

        // 初始化所有文本为不可见
        const initialVisibility: TextVisibility = {};
        ['stem', 'analysis', 'answer', 'summary'].forEach(section => {
          content[section]?.forEach(item => {
            const filename = item.url.split('/').pop() || '';
            initialVisibility[filename] = false;
          });
        });
        setTextVisibility(initialVisibility);

        // 清除之前的位置缓存，强制重新计算
        console.log('🔄 [位置重置] 清除位置缓存，准备重新计算');
        setTextPositions({});
        setStaticTextPositions({});
      } catch (error) {
        console.error('Error parsing response:', error);
      }
    }
  }, [data.new_response]);

  // 处理音频切换
  useEffect(() => {
    if (!parsedContent || !data.currentAudioFile) {
      setCurrentSubtitle('');
      setCurrentSection('');
      return;
    }

    // 当音频切换时，智能重置滚动状态
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // 延迟检查，给用户一些时间来决定是否要手动控制滚动
    scrollTimeoutRef.current = setTimeout(() => {
      if (contentRef.current && isNearBottom(contentRef.current, 150)) {
        // 如果用户在底部附近，重置滚动状态以启用自动滚动
        console.log('📜 [音频切换] 用户在底部附近，启用自动滚动');
        setUserScrolled(false);
      }
    }, 500);

    // 标记之前音频的所有标记为已完成
    const currentAudioIndex = data.audioFiles?.findIndex(audio => audio.filename === data.currentAudioFile) || 0;
    const newCompletedMarks = new Set<string>();
    const newRenderStates: {[key: string]: 'hidden' | 'animating' | 'completed'} = {};

    // 添加之前所有音频的标记到已完成集合
    data.audioFiles?.slice(0, currentAudioIndex).forEach(audioFile => {
      Object.keys(textPositions).forEach(key => {
        if (key.startsWith(audioFile.filename)) {
          newCompletedMarks.add(key);
          newRenderStates[key] = 'completed';
        }
      });
    });

    // 设置当前音频的标记为动画状态
    Object.keys(textPositions).forEach(key => {
      if (key.startsWith(data.currentAudioFile || '')) {
        newRenderStates[key] = 'animating';
      } else if (!newCompletedMarks.has(key)) {
        newRenderStates[key] = 'hidden';
      }
    });

    setCompletedMarks(newCompletedMarks);
    setMarkRenderStates(newRenderStates);

    console.log('🎯 [标记状态] 更新标记渲染状态:', newRenderStates);

    const newVisibility: TextVisibility = {};
    let foundCurrent = false;
    let currentSubtitleText = '';
    let currentSectionName = '';

    // 遍历所有部分
    ['stem', 'analysis', 'answer', 'summary'].forEach(section => {
      parsedContent[section]?.forEach(item => {
        const filename = item.url.split('/').pop() || '';
        if (foundCurrent) {
          newVisibility[filename] = false;
        } else {
          newVisibility[filename] = true;
          if (filename === data.currentAudioFile) {
            foundCurrent = true;
            // 设置当前字幕和部分
            currentSubtitleText = item.zm || '';
            currentSectionName = section;
          }
        }
      });
    });

    setTextVisibility(newVisibility);
    setCurrentSubtitle(currentSubtitleText);
    setCurrentSection(currentSectionName);

    // 启动打字机效果（如果是当前播放的音频）
    if (data.currentAudioFile && foundCurrent) {
      // 查找当前音频对应的内容和时长
      ['stem', 'analysis', 'answer', 'summary'].forEach(section => {
        parsedContent[section]?.forEach(item => {
          const filename = item.url.split('/').pop() || '';
          if (filename === data.currentAudioFile && item.yw) {
            // 获取音频时长 - 必须等待数据库时长信息
            const audioInfo = data.audioFiles?.find(audio => audio.filename === filename);
            if (audioInfo?.duration) {
              // 只有当数据库时长信息可用时才启动打字机效果
              startTypewriter(filename, item.yw, audioInfo.duration);
            } else {
              console.warn(`[QuestionContent] 等待音频时长信息: ${filename}`);
            }
          }
        });
      });
    }

    // 自动滚动到当前播放内容（如果用户没有主动滚动）
    if (!userScrolled && contentRef.current && data.currentAudioFile) {
      console.log('📜 [自动滚动] 准备自动滚动到:', data.currentAudioFile);

      // 延迟执行，确保DOM已更新
      setTimeout(() => {
        if (contentRef.current && !userScrolled) { // 再次检查用户是否滚动
          const currentElement = contentRef.current.querySelector(`[data-audio="${data.currentAudioFile}"]`) as HTMLElement;
          if (currentElement) {
            const container = contentRef.current;
            const containerHeight = container.clientHeight;
            const containerScrollTop = container.scrollTop;

            // 获取元素相对于容器的位置
            const elementTop = currentElement.offsetTop;
            const elementHeight = currentElement.offsetHeight;
            const elementBottom = elementTop + elementHeight;

            // 计算当前可视区域
            const viewportTop = containerScrollTop;
            const viewportBottom = containerScrollTop + containerHeight;

            // 底部预留空间
            const bottomPadding = 40;
            const effectiveViewportBottom = viewportBottom - bottomPadding;

            // 检查元素是否完全可见
            const isElementVisible = elementTop >= viewportTop && elementBottom <= effectiveViewportBottom;

            console.log('📜 [自动滚动] 元素可见性检查:', {
              elementTop,
              elementBottom,
              viewportTop,
              effectiveViewportBottom,
              isElementVisible
            });

            if (!isElementVisible) {
              // 如果元素不完全可见，需要滚动
              let targetScrollTop;

              if (elementHeight > containerHeight - bottomPadding) {
                // 如果元素太高，滚动到元素顶部
                targetScrollTop = elementTop;
              } else if (elementBottom > effectiveViewportBottom) {
                // 如果元素底部超出可视区域，滚动使元素底部可见
                targetScrollTop = elementBottom - containerHeight + bottomPadding;
              } else {
                // 如果元素顶部在可视区域上方，滚动到元素顶部
                targetScrollTop = elementTop;
              }

              console.log('📜 [自动滚动] 执行滚动:', {
                from: containerScrollTop,
                to: Math.max(0, targetScrollTop)
              });

              // 标记正在自动滚动
              setIsAutoScrolling(true);

              container.scrollTo({
                top: Math.max(0, targetScrollTop),
                behavior: 'smooth'
              });

              // 滚动完成后重置标记
              setTimeout(() => {
                setIsAutoScrolling(false);
              }, 800); // 给滚动动画足够时间完成
            }
          }
        }
      }, 200); // 增加延迟时间，确保DOM和样式完全更新
    }
  }, [data.currentAudioFile, parsedContent, userScrolled]);

  // 检测用户是否在底部附近
  const isNearBottom = (container: HTMLElement, threshold: number = 50): boolean => {
    const { scrollTop, scrollHeight, clientHeight } = container;
    return scrollHeight - scrollTop - clientHeight <= threshold;
  };

  // 智能滚动检测函数
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;

    // 如果正在自动滚动，忽略此次滚动事件
    if (isAutoScrolling) {
      console.log('📜 [滚动] 忽略自动滚动触发的事件');
      return;
    }

    // 清除之前的定时器
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // 检查用户是否在底部附近（更宽松的阈值）
    const nearBottom = isNearBottom(target, 100);
    const { scrollTop, scrollHeight, clientHeight } = target;

    console.log('📜 [滚动] 滚动状态:', {
      scrollTop,
      scrollHeight,
      clientHeight,
      distanceFromBottom: scrollHeight - scrollTop - clientHeight,
      nearBottom,
      userScrolled
    });

    if (nearBottom) {
      // 用户滚动到底部附近，恢复自动滚动
      if (userScrolled) {
        console.log('📜 [滚动] 用户回到底部，恢复自动滚动');
        setUserScrolled(false);
      }
    } else {
      // 用户向上滚动，暂停自动滚动
      if (!userScrolled) {
        console.log('📜 [滚动] 用户向上滚动，暂停自动滚动');
        setUserScrolled(true);
      }
    }

    // 设置延迟检测，防止滚动过程中频繁触发
    scrollTimeoutRef.current = setTimeout(() => {
      // 再次检查是否在底部
      const finalNearBottom = isNearBottom(target, 100);
      if (finalNearBottom && userScrolled) {
        console.log('📜 [滚动] 延迟检测：用户在底部，恢复自动滚动');
        setUserScrolled(false);
      } else if (!finalNearBottom && !userScrolled) {
        console.log('📜 [滚动] 延迟检测：用户不在底部，暂停自动滚动');
        setUserScrolled(true);
      }
    }, 200);
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      Object.values(typewriterTimers.current).forEach(timer => {
        if (timer) clearTimeout(timer);
      });
      Object.values(cursorTimers.current).forEach(timer => {
        if (timer) clearTimeout(timer);
      });
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  // 计算文本位置和宽度
  useEffect(() => {
    if (!parsedContent || !stemTextRef.current || !data.stem_txt) return;

    // 延迟计算，确保DOM完全稳定
    const calculatePositions = () => {
      console.log('📏 [位置计算] 开始计算文本位置', {
        isReadingPhase,
        containerWidth: stemTextRef.current?.offsetWidth,
        containerHeight: stemTextRef.current?.offsetHeight
      });

      const positions: {[key: string]: TextPosition} = {};
      const containerWidth = stemTextRef.current!.offsetWidth;

      const measureContainer = document.createElement('div');
      measureContainer.style.cssText = `
        position: absolute;
        visibility: hidden;
        width: ${containerWidth}px;
        font: ${window.getComputedStyle(stemTextRef.current!).font};
        line-height: 1.5;
        white-space: pre-wrap;
        padding: 0;
        margin: 0;
      `;
      document.body.appendChild(measureContainer);

      parsedContent.stem?.forEach(item => {
        if (item.mark && item.mark.length > 0 && data.stem_txt) {
          const filename = item.url.split('/').pop() || '';

          // 先按照text在原文中的位置对标记进行排序
          const sortedMarks = [...item.mark].sort((a, b) => {
            const indexA = data.stem_txt!.indexOf(a.text);
            const indexB = data.stem_txt!.indexOf(b.text);
            return indexA - indexB;
          });

          // 为每个排序后的标记计算位置
          sortedMarks.forEach((mark, sortedIndex) => {
            const index = data.stem_txt!.indexOf(mark.text);
            if (index !== -1) {
              // 创建完整的文本内容
              measureContainer.textContent = data.stem_txt!;

              // 创建范围来精确测量位置
              const range = document.createRange();
              const textNode = measureContainer.firstChild as Text;

              // 设置范围为目标文本
              range.setStart(textNode, index);
              range.setEnd(textNode, index + mark.text.length);

              // 获取范围的客户端矩形
              const rects = range.getClientRects();
              const lines: { start: number; end: number; top: number; baseline: number; }[] = [];

              Array.from(rects).forEach(rect => {
                const containerRect = measureContainer.getBoundingClientRect();
                lines.push({
                  start: rect.left - containerRect.left,
                  end: rect.right - containerRect.left,
                  top: rect.top - containerRect.top,
                  baseline: rect.bottom - containerRect.top - 2
                });
              });

              // 使用排序后的索引作为键
              const markKey = `${filename}-mark-${sortedIndex}`;
              positions[markKey] = {
                text: mark.text,
                type: mark.type,
                lines,
                textIndex: index, // 保存文本在原文中的位置，用于时间计算
                filename: filename // 保存文件名，用于时间计算
              };
            }
          });
        }
      });

      document.body.removeChild(measureContainer);

      // 计算未标记的文本段并添加直线标记
      calculateUnmarkedTextPositions(positions);

      setTextPositions(positions);

      // 计算标记动画时间
      calculateMarkAnimationTiming(positions);

      console.log('📏 [位置计算] 文本位置计算完成', {
        totalPositions: Object.keys(positions).length,
        isReadingPhase
      });
    };

    // 使用 requestAnimationFrame 确保DOM渲染完成
    requestAnimationFrame(() => {
      // 再次延迟确保布局稳定
      setTimeout(calculatePositions, 100);
    });
  }, [parsedContent, data.stem_txt, data.currentAudioFile, data.audioFiles]);

  // 计算未标记文本的位置并添加直线标记
  const calculateUnmarkedTextPositions = (positions: {[key: string]: TextPosition}) => {
    if (!data.stem_txt || !parsedContent?.stem) return;

    console.log('📏 [未标记文本] 开始计算未标记文本位置');

    // 按文件分组现有标记
    const marksByFile: {[filename: string]: Array<{start: number, end: number}>} = {};

    // 收集每个文件中已标记的文本范围
    parsedContent.stem.forEach(item => {
      const filename = item.url.split('/').pop() || '';
      if (item.mark && item.mark.length > 0 && item.yw) {
        marksByFile[filename] = [];

        item.mark.forEach(mark => {
          const markIndex = item.yw!.indexOf(mark.text);
          if (markIndex !== -1) {
            marksByFile[filename].push({
              start: markIndex,
              end: markIndex + mark.text.length
            });
          }
        });

        // 按起始位置排序
        marksByFile[filename].sort((a, b) => a.start - b.start);
        console.log(`📏 [未标记文本] 文件 ${filename} 已标记范围:`, marksByFile[filename]);
      }
    });

    // 为每个文件计算未标记的文本段
    parsedContent.stem.forEach(item => {
      const filename = item.url.split('/').pop() || '';
      if (!item.yw) return;

      const text = item.yw;
      const markedRanges = marksByFile[filename] || [];
      const unmarkedSegments: Array<{start: number, end: number, text: string}> = [];

      console.log(`📏 [未标记文本] 处理文件 ${filename}, 文本: "${text}"`);

      if (markedRanges.length === 0) {
        // 如果没有任何标记，整个文本都是未标记的
        unmarkedSegments.push({
          start: 0,
          end: text.length,
          text: text
        });
      } else {
        let currentPos = 0;

        // 检查每个标记之间的间隙
        markedRanges.forEach((range, index) => {
          // 添加标记前的未标记段
          if (currentPos < range.start) {
            const segmentText = text.slice(currentPos, range.start).trim();
            if (segmentText.length > 0) {
              unmarkedSegments.push({
                start: currentPos,
                end: range.start,
                text: segmentText
              });
            }
          }
          currentPos = Math.max(currentPos, range.end);
        });

        // 添加最后一个标记后的未标记段
        if (currentPos < text.length) {
          const segmentText = text.slice(currentPos).trim();
          if (segmentText.length > 0) {
            unmarkedSegments.push({
              start: currentPos,
              end: text.length,
              text: segmentText
            });
          }
        }
      }

      console.log(`📏 [未标记文本] 文件 ${filename} 未标记段:`, unmarkedSegments);

      // 为每个未标记段创建位置信息
      unmarkedSegments.forEach((segment, segmentIndex) => {
        if (segment.text.trim().length === 0) return;

        // 创建测量容器
        const measureContainer = document.createElement('div');
        measureContainer.style.position = 'absolute';
        measureContainer.style.visibility = 'hidden';
        measureContainer.style.whiteSpace = 'pre-wrap';
        measureContainer.style.font = window.getComputedStyle(stemTextRef.current!).font;
        measureContainer.style.lineHeight = window.getComputedStyle(stemTextRef.current!).lineHeight;
        measureContainer.style.width = window.getComputedStyle(stemTextRef.current!).width;
        document.body.appendChild(measureContainer);

        // 创建完整的文本内容
        measureContainer.textContent = data.stem_txt!;

        // 创建范围来精确测量位置
        const range = document.createRange();
        const textNode = measureContainer.firstChild as Text;

        // 找到segment在stem_txt中的实际位置
        const stemIndex = data.stem_txt!.indexOf(segment.text);
        if (stemIndex !== -1) {
          // 设置范围为目标文本
          range.setStart(textNode, stemIndex);
          range.setEnd(textNode, stemIndex + segment.text.length);

          // 获取范围的客户端矩形
          const rects = range.getClientRects();
          const lines: { start: number; end: number; top: number; baseline: number; }[] = [];

          Array.from(rects).forEach(rect => {
            const containerRect = measureContainer.getBoundingClientRect();
            lines.push({
              start: rect.left - containerRect.left,
              end: rect.right - containerRect.left,
              top: rect.top - containerRect.top,
              baseline: rect.bottom - containerRect.top - 2
            });
          });

          // 添加到positions中
          const markKey = `${filename}-straight-${segmentIndex}`;
          positions[markKey] = {
            text: segment.text,
            type: 'straight',
            lines,
            textIndex: stemIndex,
            filename: filename
          };

          console.log(`📏 [未标记文本] 添加直线标记: ${markKey}`, {
            text: segment.text,
            lines: lines.length,
            textIndex: stemIndex
          });
        }

        document.body.removeChild(measureContainer);
      });
    });
  };

  // 计算标记动画时间（优化算法）
  const calculateMarkAnimationTiming = (positions: {[key: string]: TextPosition}) => {
    console.log('🎯 [标记动画] 开始计算标记动画时间');
    console.log('🎯 [标记动画] 输入positions:', positions);
    console.log('🎯 [标记动画] 音频文件列表:', data.audioFiles);
    console.log('🎯 [标记动画] 解析内容:', parsedContent);

    const timing: {[key: string]: {delay: number, duration: number}} = {};

    // 按文件分组标记
    const marksByFile: {[filename: string]: Array<{key: string, position: TextPosition}>} = {};

    Object.entries(positions).forEach(([key, position]) => {
      if (position.filename && position.textIndex !== undefined) {
        if (!marksByFile[position.filename]) {
          marksByFile[position.filename] = [];
        }
        marksByFile[position.filename].push({key, position});
        console.log(`🎯 [标记动画] 添加标记到文件 ${position.filename}:`, {key, text: position.text, textIndex: position.textIndex});
      }
    });

    console.log('🎯 [标记动画] 按文件分组的标记:', marksByFile);

    // 计算每个模块的起始时间
    let cumulativeTime = 0;
    const moduleStartTimes: {[filename: string]: number} = {};

    // 按照音频文件顺序计算累积时间 - 必须等待所有音频时长信息
    if (data.audioFiles) {
      // 检查是否所有音频都有时长信息
      const allHaveDuration = data.audioFiles.every(audioFile => audioFile.duration);
      if (!allHaveDuration) {
        console.warn('🎯 [标记动画] 等待所有音频时长信息加载完成');
        return; // 等待时长信息完整后再计算
      }

      data.audioFiles.forEach(audioFile => {
        moduleStartTimes[audioFile.filename] = cumulativeTime;
        const duration = audioFile.duration!; // 确保有时长信息
        console.log(`🎯 [标记动画] 模块 ${audioFile.filename} 起始时间: ${cumulativeTime}秒, 时长: ${duration}秒 (数据库)`);
        cumulativeTime += duration;
      });
    }

    console.log('🎯 [标记动画] 模块起始时间映射:', moduleStartTimes);

    // 为每个文件的标记计算时间
    Object.entries(marksByFile).forEach(([filename, marks]) => {
      console.log(`🎯 [标记动画] 处理文件 ${filename} 的标记`);

      // 获取音频时长和起始时间 - 确保有时长信息
      const audioInfo = data.audioFiles?.find(audio => audio.filename === filename);
      if (!audioInfo?.duration) {
        console.warn(`🎯 [标记动画] 跳过文件 ${filename} - 缺少时长信息`);
        return; // 跳过没有时长信息的文件
      }

      const audioDuration = audioInfo.duration;
      const moduleStartTime = moduleStartTimes[filename] || 0;

      console.log(`🎯 [标记动画] 文件 ${filename} - 时长: ${audioDuration}秒 (数据库), 起始时间: ${moduleStartTime}秒`);

      // 获取该模块的文本内容
      const moduleItem = parsedContent?.stem?.find(item =>
        item.url.split('/').pop() === filename
      );
      const moduleText = moduleItem?.yw || '';
      const moduleTextLength = moduleText.length;

      console.log(`🎯 [标记动画] 文件 ${filename} - 模块文本: "${moduleText}", 长度: ${moduleTextLength}`);

      // 按文本位置排序
      marks.sort((a, b) => a.position.textIndex! - b.position.textIndex!);

      // 计算每个标记的时间 - 按行分别计算
      marks.forEach((mark) => {
        console.log(`🎯 [标记动画] 处理标记: "${mark.position.text}"`);

        // 计算标记文本在模块文本中的位置
        const markTextInModule = moduleText.indexOf(mark.position.text);
        if (markTextInModule === -1) {
          console.log(`🎯 [标记动画] ❌ 标记文本 "${mark.position.text}" 在模块文本中未找到`);
          return; // 如果找不到，跳过
        }

        const markTextLength = mark.position.text.length;
        const lines = mark.position.lines;

        console.log(`🎯 [标记动画] 标记 "${mark.position.text}" 有 ${lines.length} 行`);

        if (lines.length === 1) {
          // 单行标记，使用原有逻辑
          const markStartInModule = markTextInModule / moduleTextLength;
          const markDurationRatio = markTextLength / moduleTextLength;
          const relativeDelay = markStartInModule * audioDuration;
          const markDuration = Math.max(markDurationRatio * audioDuration, 0.5);

          console.log(`🎯 [标记动画] 单行标记 "${mark.position.text}" 计算结果:`);
          console.log(`   - 相对延迟时间: ${relativeDelay.toFixed(2)}秒`);
          console.log(`   - 持续时间: ${markDuration.toFixed(2)}秒`);

          timing[mark.key] = {
            delay: relativeDelay,
            duration: markDuration
          };
        } else {
          // 多行标记，为每行分别计算时间
          console.log(`🎯 [标记动画] 多行标记 "${mark.position.text}" 按行计算时间`);

          // 估算每行的文本长度（基于行宽度比例）
          const totalWidth = lines.reduce((sum, line) => sum + (line.end - line.start), 0);
          let cumulativeDelay = markTextInModule / moduleTextLength * audioDuration;

          lines.forEach((line, lineIndex) => {
            const lineWidth = line.end - line.start;
            const lineTextRatio = lineWidth / totalWidth;
            const lineTextLength = Math.round(markTextLength * lineTextRatio);
            const lineDurationRatio = lineTextLength / moduleTextLength;
            const lineDuration = Math.max(lineDurationRatio * audioDuration, 0.3); // 每行最小0.3秒

            const lineKey = lines.length > 1 ? `${mark.key}-line-${lineIndex}` : mark.key;

            timing[lineKey] = {
              delay: cumulativeDelay,
              duration: lineDuration
            };

            console.log(`🎯 [标记动画] 第${lineIndex + 1}行 (${lineKey}):`);
            console.log(`   - 行宽度比例: ${(lineTextRatio * 100).toFixed(1)}%`);
            console.log(`   - 估算文本长度: ${lineTextLength}`);
            console.log(`   - 延迟时间: ${cumulativeDelay.toFixed(2)}秒`);
            console.log(`   - 持续时间: ${lineDuration.toFixed(2)}秒`);

            // 下一行的延迟时间 = 当前行的延迟时间 + 当前行的持续时间
            cumulativeDelay += lineDuration;
          });
        }
      });
    });

    console.log('🎯 [标记动画] 最终时间配置:', timing);
    setMarkAnimationTiming(timing);
  };

  // 渲染静态标记（用于讲解阶段的左侧题目区域）
  const renderStaticMarkers = () => {
    console.log('🎨 [静态标记] 开始渲染静态标记');

    // 只渲染已完成的波浪线和圈选标记，不渲染直线标记
    const staticMarks: Array<{
      key: string;
      position: TextPosition;
    }> = [];

    Object.entries(staticTextPositions).forEach(([key, position]) => {
      // 只渲染波浪线和圈选标记，跳过直线标记
      if (position.filename && (position.type === 'wave' || position.type === 'circle')) {
        // 检查是否为多行标记
        if (position.lines.length > 1) {
          // 多行标记：为每行创建单独的标记条目
          position.lines.forEach((line, lineIndex) => {
            staticMarks.push({
              key: `${key}-line-${lineIndex}`,
              position: {
                ...position,
                lines: [line] // 每行只包含自己的line信息
              }
            });
          });
        } else {
          // 单行标记
          staticMarks.push({
            key,
            position
          });
        }
      }
    });

    console.log(`🎨 [静态标记] 总共收集到 ${staticMarks.length} 个静态标记需要渲染`);

    // 渲染静态标记
    return staticMarks.map((mark) => {
      const { key, position } = mark;
      const line = position.lines[0];
      const width = line.end - line.start;
      const height = Math.max(line.baseline - line.top + 12, 28);

      console.log(`🎨 [静态标记] 渲染静态标记 ${key}:`, {
        type: position.type,
        text: position.text,
        width,
        height,
        line
      });

      if (position.type === 'wave') {
        // 渲染静态波浪线
        const phase = (line.start * 0.3) % (2 * Math.PI);

        return (
          <div
            key={key}
            className="absolute opacity-100"
            style={{
              left: `${line.start}px`,
              top: `${line.baseline + 3}px`,
              height: '1px'
            }}
          >
            <WavyUnderline
              width={width}
              duration={0} // 静态显示，无动画
              amplitude={1}
              phase={phase}
              delay={0}
            />
          </div>
        );
      } else if (position.type === 'circle') {
        // 渲染静态圈选标记
        return (
          <div
            key={key}
            className="absolute opacity-100"
            style={{
              left: `${line.start - 6}px`,
              top: `${line.top - 8}px`,
              width: `${width + 12}px`,
              height: `${height + 8}px`
            }}
          >
            <CircleMarker
              width={width + 12}
              height={height + 8}
              duration={0} // 静态显示，无动画
              delay={0}
            />
          </div>
        );
      }

      return null;
    }).filter(Boolean);
  };

  // 渲染标记（波浪线和圈选）- 优化版本，避免重复渲染
  const renderMarkers = () => {
    console.log('🎨 [标记渲染] 开始渲染标记');
    console.log('🎨 [标记渲染] 当前音频文件:', data.currentAudioFile);
    console.log('🎨 [标记渲染] 已完成标记:', completedMarks);

    // 收集所有需要显示的标记，基于渲染状态
    const allMarks: Array<{
      key: string;
      position: TextPosition;
      timing: {delay: number, duration: number};
      renderState: 'animating' | 'completed';
    }> = [];

    Object.entries(textPositions).forEach(([key, position]) => {
      const currentRenderState = markRenderStates[key];

      // 只渲染处于动画或完成状态的标记
      if (position.filename && (currentRenderState === 'animating' || currentRenderState === 'completed')) {

        // 检查是否为多行标记
        if (position.lines.length > 1) {
          // 多行标记：为每行创建单独的标记条目
          position.lines.forEach((line, lineIndex) => {
            const lineKey = `${key}-line-${lineIndex}`;
            const lineTiming = markAnimationTiming[lineKey];

            if (lineTiming) {
              allMarks.push({
                key: lineKey,
                position: {
                  ...position,
                  lines: [line] // 每行只包含自己的line信息
                },
                timing: lineTiming,
                renderState: currentRenderState
              });
              console.log(`🎨 [标记渲染] ✅ 添加多行标记第${lineIndex}行: ${lineKey} (状态: ${currentRenderState})`);
            }
          });
        } else {
          // 单行标记：使用原有逻辑
          const timing = markAnimationTiming[key];
          if (timing) {
            allMarks.push({
              key,
              position,
              timing,
              renderState: currentRenderState
            });
            console.log(`🎨 [标记渲染] ✅ 添加单行标记: ${key} (状态: ${currentRenderState})`);
          }
        }
      } else {
        console.log(`🎨 [标记渲染] ❌ 跳过标记: ${key} (状态: ${currentRenderState})`);
      }
    });

    console.log(`🎨 [标记渲染] 总共收集到 ${allMarks.length} 个标记需要渲染`);

    // 按延迟时间排序
    allMarks.sort((a, b) => a.timing.delay - b.timing.delay);

    // 渲染标记 - 统一处理动画和完成状态
    return allMarks.map((mark) => {
      const { key, position, timing, renderState } = mark;
      const isCompleted = renderState === 'completed';

      console.log(`🎨 [标记渲染] 渲染标记 ${key}:`, {
        type: position.type,
        text: position.text,
        timing,
        renderState,
        lines: position.lines.length
      });

      // 现在每个position只包含一行，直接处理
      const line = position.lines[0];
      const width = line.end - line.start;
      const height = Math.max(line.baseline - line.top + 12, 28); // 增加高度，最小高度28px

      // 统一的标记参数
      const markProps = {
        delay: isCompleted ? 0 : timing.delay,
        duration: isCompleted ? 0 : timing.duration,
        // 使用稳定的key，避免不必要的组件重新创建
        key: key,
        // 添加状态标识用于调试
        renderState: renderState
      };

      console.log(`🎨 [标记渲染] 渲染标记 ${key}:`, {
        width,
        height,
        line,
        markProps,
        renderState,
        timing: timing
      });

        if (position.type === 'wave') {
          // 渲染波浪线
          const phase = (line.start * 0.3) % (2 * Math.PI);

          return (
            <div
              key={markProps.key}
              className="absolute transition-opacity duration-300 opacity-100"
              style={{
                left: `${line.start}px`,
                top: `${line.baseline + 3}px`,
                height: '1px'
              }}
            >
              <WavyUnderline
                width={width}
                duration={markProps.duration}
                amplitude={1}
                phase={phase}
                delay={markProps.delay}
              />
            </div>
          );
        } else if (position.type === 'circle') {
          // 渲染圈选标记
          return (
            <div
              key={markProps.key}
              className="absolute transition-opacity duration-300 opacity-100"
              style={{
                left: `${line.start - 6}px`, // 向左扩展6px
                top: `${line.top - 8}px`,   // 向上扩展8px
                width: `${width + 12}px`,    // 宽度增加12px
                height: `${height + 8}px`   // 高度增加8px
              }}
            >
              <CircleMarker
                width={width + 12}
                height={height + 8}
                duration={markProps.duration}
                delay={markProps.delay}
              />
            </div>
          );
        } else if (position.type === 'straight') {
          // 渲染直线标记
          return (
            <div
              key={markProps.key}
              className="absolute transition-opacity duration-300 opacity-100"
              style={{
                left: `${line.start}px`,
                top: `${line.baseline + 3}px`,
                height: '4px'
              }}
            >
              <StraightUnderline
                width={width}
                duration={markProps.duration}
                delay={markProps.delay}
              />
            </div>
          );
        }

        return null;
    }).filter(Boolean);
  };

  // 修改 renderContentBlock 函数 - 支持动态样式和自动滚动
  const renderContentBlock = (section: keyof ParsedContent) => {
    if (!parsedContent) return null;

    // 统一渲染所有内容项
    const items = parsedContent[section] || [];
    if (items.length === 0) return null;

    // 获取部分标题和图标
    const getSectionInfo = (section: keyof ParsedContent) => {
      switch (section) {
        case 'analysis':
          return { title: '分析', icon: ChartBarIcon };
        case 'answer':
          return { title: '解答', icon: LightBulbIcon };
        case 'summary':
          return { title: '总结', icon: DocumentTextIcon };
        default:
          return null;
      }
    };

    const sectionInfo = getSectionInfo(section);
    const hasVisibleItems = items.some(item => {
      const filename = item.url.split('/').pop() || '';
      return textVisibility[filename];
    });

    // 判断是否应该显示fl字段作为小标题
    const shouldShowFlTitle = (item: any, section: keyof ParsedContent) => {
      if (!item.fl) return false;

      // 过滤掉系统字段
      const systemFields = ['analysis', 'summary', 'answer', 'stem'];
      return !systemFields.includes(item.fl.toLowerCase());
    };

    return (
      <div className="space-y-1">
        {/* 部分标题 - 只有当该部分有可见内容时才显示 */}
        {sectionInfo && hasVisibleItems && (
          <div className="flex items-center space-x-2 mb-2">
            <div className="bg-[#F57D23] text-white px-3 py-1 rounded flex items-center space-x-1">
              <sectionInfo.icon className="w-4 h-4" />
              <span className="font-medium">{sectionInfo.title}</span>
            </div>
          </div>
        )}

        {items.map((item, index) => {
          const filename = item.url.split('/').pop() || '';
          const isVisible = textVisibility[filename] || section === 'stem';
          const isCurrentlyPlaying = data.currentAudioFile === filename;

          return item.yw && (
            <div
              key={index}
              data-audio={filename}
              className={`transition-all duration-300 rounded-xl p-4 ${
                isVisible ? 'opacity-100' : 'opacity-0'
              } ${
                isCurrentlyPlaying
                  ? 'bg-[#FADCB0]'
                  : 'bg-transparent'
              }`}
            >
              {/* 显示分类标题（如果应该显示） */}
              {shouldShowFlTitle(item, section) && (
                <div className="text-[#F57D23] font-bold text-sm mb-1">
                  {item.fl}
                </div>
              )}
              {/* 显示内容 */}
              <div className={`text-[#643C2B] transition-all duration-300 typewriter-text ${
                isCurrentlyPlaying ? 'text-xl font-medium leading-[1.6]' : 'text-base leading-[1.5]'
              }`}>
                {isCurrentlyPlaying && typewriterText[filename] !== undefined ? (
                  <span className="relative">
                    {renderProcessedText(typewriterText[filename], true)}
                    {/* 打字机光标 */}
                    {typewriterCursor[filename] && (
                      <span className="inline-block w-0.5 h-5 bg-[#F57D23] ml-0.5 typewriter-cursor">
                      </span>
                    )}
                  </span>
                ) : (
                  renderProcessedText(item.yw, false)
                )}
              </div>
            </div>
          );
        })}
      </div>
    );
  };





  // 判断当前是否在读题阶段（stem音频播放中）
  const isReadingPhase = parsedContent?.stem?.some(
    item => item.url.split('/').pop() === data.currentAudioFile
  );

  // 计算静态文本位置（用于讲解阶段的左侧区域）
  const calculateStaticTextPositions = () => {
    if (!stemTextRef.current || !data.stem_txt || isReadingPhase) return;

    console.log('📏 [静态位置] 开始计算静态文本位置', {
      isReadingPhase,
      hasTextPositions: Object.keys(textPositions).length > 0,
      containerWidth: stemTextRef.current.offsetWidth
    });

    // 等待布局稳定后再计算
    setTimeout(() => {
      // 再次检查DOM状态
      if (!stemTextRef.current || isReadingPhase) {
        console.log('📏 [静态位置] DOM状态已变化，取消计算');
        return;
      }

      const staticPositions: {[key: string]: TextPosition} = {};

      // 只复制波浪线和圈选标记的位置信息，重新测量位置
      Object.entries(textPositions).forEach(([key, position]) => {
        if (position.type === 'wave' || position.type === 'circle') {
          // 重新测量文本位置（基于收缩后的布局）
          const measureContainer = document.createElement('div');
          measureContainer.style.position = 'absolute';
          measureContainer.style.visibility = 'hidden';
          measureContainer.style.whiteSpace = 'pre-wrap';
          measureContainer.style.font = window.getComputedStyle(stemTextRef.current!).font;
          measureContainer.style.lineHeight = window.getComputedStyle(stemTextRef.current!).lineHeight;
          measureContainer.style.width = window.getComputedStyle(stemTextRef.current!).width;
          document.body.appendChild(measureContainer);

          measureContainer.textContent = data.stem_txt!;

          const range = document.createRange();
          const textNode = measureContainer.firstChild as Text;

          if (position.textIndex !== undefined) {
            range.setStart(textNode, position.textIndex);
            range.setEnd(textNode, position.textIndex + position.text.length);

            const rects = range.getClientRects();
            const lines: { start: number; end: number; top: number; baseline: number; }[] = [];

            Array.from(rects).forEach(rect => {
              const containerRect = measureContainer.getBoundingClientRect();
              lines.push({
                start: rect.left - containerRect.left,
                end: rect.right - containerRect.left,
                top: rect.top - containerRect.top,
                baseline: rect.bottom - containerRect.top - 2
              });
            });

            staticPositions[key] = {
              ...position,
              lines
            };

            console.log(`📏 [静态位置] 重新计算标记位置: ${key}`, {
              text: position.text,
              lines: lines.length,
              originalLines: position.lines.length
            });
          }

          document.body.removeChild(measureContainer);
        }
      });

      setStaticTextPositions(staticPositions);
      console.log('📏 [静态位置] 静态位置计算完成', {
        totalStaticPositions: Object.keys(staticPositions).length,
        originalPositions: Object.keys(textPositions).length
      });
    }, 1200); // 稍微增加延迟，确保过渡动画完全完成
  };

  // 监听阶段切换，重新计算静态位置
  useEffect(() => {
    if (!isReadingPhase) {
      calculateStaticTextPositions();
    }
  }, [parsedContent, data.currentAudioFile, textPositions]);

  // 监听文本可见性变化，检查是否需要恢复自动滚动
  useEffect(() => {
    if (!userScrolled || !contentRef.current) return;

    // 延迟检查，等待DOM更新完成
    const checkScrollPosition = setTimeout(() => {
      if (contentRef.current && isNearBottom(contentRef.current, 120)) {
        console.log('📜 [内容变化] 用户仍在底部附近，恢复自动滚动');
        setUserScrolled(false);
      }
    }, 300);

    return () => clearTimeout(checkScrollPosition);
  }, [textVisibility, userScrolled]);

  // 监听isAnimation变化，清除位置缓存
  useEffect(() => {
    console.log('🔄 [动画状态] isAnimation变化，清除位置缓存', { isAnimation });

    // 清除所有位置缓存，强制重新计算
    setTextPositions({});
    setStaticTextPositions({});

    // 重置滚动状态
    setUserScrolled(false);
    setIsAutoScrolling(false);
  }, [isAnimation]);

  return (
    <div className="h-full flex flex-col text-sm bg-[#F0F8FF]">
      <style>{katexOverrideStyles}</style>
      <style>{`
        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0; }
        }
        .typewriter-cursor {
          animation: blink 1s infinite;
        }
        @keyframes fadeInMath {
          0% { opacity: 0; transform: translateY(2px); }
          100% { opacity: 1; transform: translateY(0); }
        }
        .math-fade-in {
          animation: fadeInMath 0.3s ease-out;
        }
        .typewriter-text {
          word-break: break-word;
          line-height: 1.6;
        }
      `}</style>

      {/* 主内容区域 - 占据除字幕外的所有空间 */}
      <div className="flex-1 min-h-0">
        <div className="h-full flex p-4 transition-all duration-1000 ease-in-out">
          {/* 题目区域 - 动态宽度 */}
          <div className={`flex flex-col transition-all duration-1000 ease-in-out ${
            isReadingPhase ? 'w-full' : 'w-1/3 pr-4'
          }`}>
            <div className="flex items-start mb-4">
              <div className="bg-[#F57D23] text-white px-3 py-1 rounded flex items-center space-x-1 whitespace-nowrap">
                <BookOpenIcon className="w-4 h-4" />
                <span className="font-medium">题目</span>
              </div>
            </div>
            <div className="flex-grow overflow-y-auto">
              <div className="relative">
                <div
                  ref={stemTextRef}
                  className={`text-[#643C2B] transition-all duration-1000 ease-in-out ${
                    isReadingPhase ? 'text-base leading-relaxed' : 'text-base leading-[1.5]'
                  }`}
                >
                  {data.stem_txt || data.content}
                </div>
                {/* 动画标记层 - 仅在读题阶段显示 */}
                {isAnimation && isReadingPhase && (
                  <div className="absolute inset-0 pointer-events-none">
                    {renderMarkers()}
                  </div>
                )}
                {/* 静态标记层 - 仅在讲解阶段显示 */}
                {!isReadingPhase && (
                  <div className="absolute inset-0 pointer-events-none">
                    {renderStaticMarkers()}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 中间分隔线 - 仅在讲解阶段显示 */}
          <div className={`transition-all duration-1000 ease-in-out ${
            isReadingPhase ? 'w-0 opacity-0' : 'w-px bg-gray-300 mx-4 opacity-100'
          }`}></div>

          {/* 右侧：解析内容 - 仅在讲解阶段显示 */}
          <div className={`flex flex-col pl-4 transition-all duration-1000 ease-in-out ${
            isReadingPhase ? 'w-0 opacity-0 overflow-hidden' : 'w-2/3 opacity-100'
          }`}>
            <div
              ref={contentRef}
              className="flex-grow overflow-y-auto hide-scrollbar space-y-3 p-4"
              onScroll={handleScroll}
            >
              {/* 分析内容 */}
              <div>
                {renderContentBlock('analysis')}
              </div>

              {/* 解答内容 */}
              <div>
                {renderContentBlock('answer')}
              </div>

              {/* 总结内容 */}
              <div>
                {renderContentBlock('summary')}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 字幕区域 - 固定在底部一行 */}
      {isAnimation && (
        <div className="flex-shrink-0 h-16 flex items-center justify-center px-4">
          <div className="text-[#F57D23] text-sm leading-relaxed font-medium text-center">
            {currentSubtitle ? renderProcessedText(currentSubtitle) : ''}
          </div>
        </div>
      )}
    </div>
  );
}