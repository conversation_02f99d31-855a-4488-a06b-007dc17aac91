#!/usr/bin/env node

/**
 * 安装视频录制功能所需的依赖包
 * 运行方式: node scripts/install-video-deps.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🎥 开始安装视频录制功能依赖...\n');

// 需要安装的依赖包
const dependencies = [
  'uuid',                    // UUID生成
  '@types/uuid'              // UUID类型定义
];

const devDependencies = [
  'fluent-ffmpeg',          // FFmpeg Node.js包装器
  'ffmpeg-static',          // 静态FFmpeg二进制文件
  '@types/fluent-ffmpeg'    // FFmpeg类型定义
];

try {
  // 检查package.json是否存在
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  if (!fs.existsSync(packageJsonPath)) {
    throw new Error('package.json not found. Please run this script from the project root.');
  }

  console.log('📦 安装生产依赖...');
  if (dependencies.length > 0) {
    const depsCommand = `npm install ${dependencies.join(' ')}`;
    console.log(`执行: ${depsCommand}`);
    execSync(depsCommand, { stdio: 'inherit' });
  }

  console.log('\n📦 安装开发依赖...');
  if (devDependencies.length > 0) {
    const devDepsCommand = `npm install --save-dev ${devDependencies.join(' ')}`;
    console.log(`执行: ${devDepsCommand}`);
    execSync(devDepsCommand, { stdio: 'inherit' });
  }

  console.log('\n✅ 视频录制功能依赖安装完成！');
  console.log('\n📋 已安装的包:');
  console.log('生产依赖:');
  dependencies.forEach(dep => console.log(`  - ${dep}`));
  console.log('开发依赖:');
  devDependencies.forEach(dep => console.log(`  - ${dep}`));

  console.log('\n🎬 视频录制功能现在可以使用了！');
  console.log('\n注意事项:');
  console.log('1. FFmpeg功能仅在服务器端可用');
  console.log('2. 客户端录制使用浏览器原生MediaRecorder API');
  console.log('3. 视频转换需要服务器支持，如果转换失败会提供WebM格式下载');
  console.log('4. 建议在生产环境中使用外部FFmpeg服务以获得更好的性能');

} catch (error) {
  console.error('\n❌ 安装失败:', error.message);
  console.error('\n可能的解决方案:');
  console.error('1. 确保在项目根目录运行此脚本');
  console.error('2. 检查网络连接');
  console.error('3. 尝试清理npm缓存: npm cache clean --force');
  console.error('4. 手动安装依赖:');
  console.error(`   npm install ${dependencies.join(' ')}`);
  console.error(`   npm install --save-dev ${devDependencies.join(' ')}`);
  
  process.exit(1);
}
