import { create } from 'zustand';
import { Task } from '@/types/database';

interface TaskStore {
  // 搜索相关
  searchId: string;
  setSearchId: (id: string) => void;
  
  // 分页相关
  currentPage: number;
  pageSize: number;
  totalItems: number;
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
  setTotalItems: (total: number) => void;
  
  // 任务数据
  tasks: Task[];
  setTasks: (tasks: Task[]) => void;
  
  // 加载状态
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  
  // 题目内容
  content: string;
  setContent: (content: string) => void;
}

export const useTaskStore = create<TaskStore>((set) => ({
  // 搜索相关
  searchId: '',
  setSearchId: (id) => set({ searchId: id }),
  
  // 分页相关
  currentPage: 1,
  pageSize: 5,
  totalItems: 0,
  setCurrentPage: (page) => set({ currentPage: page }),
  setPageSize: (size) => set({ pageSize: size }),
  setTotalItems: (total) => set({ totalItems: total }),
  
  // 任务数据
  tasks: [],
  setTasks: (tasks) => set({ tasks }),
  
  // 加载状态
  isLoading: false,
  setIsLoading: (loading) => set({ isLoading: loading }),
  
  // 题目内容
  content: '',
  setContent: (content) => set({ content }),
})); 