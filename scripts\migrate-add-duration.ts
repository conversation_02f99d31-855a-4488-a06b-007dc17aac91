#!/usr/bin/env tsx

import { createConnection } from '../config/database';

function migrateDuration() {
  console.log('🔄 开始添加音频时长字段迁移...');
  
  const db = createConnection();
  
  try {
    // 检查是否已经存在duration字段
    const tableInfo = db.prepare("PRAGMA table_info(audio)").all() as any[];
    const hasDuration = tableInfo.some(column => column.name === 'duration');
    
    if (hasDuration) {
      console.log('✅ duration字段已存在，跳过迁移');
      return;
    }
    
    // 添加duration字段
    db.prepare(`
      ALTER TABLE audio 
      ADD COLUMN duration REAL DEFAULT NULL
    `).run();
    
    console.log('✅ 成功添加duration字段到audio表');
    
  } catch (error) {
    console.error('❌ 迁移失败:', error);
    throw error;
  } finally {
    db.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  migrateDuration();
}

export { migrateDuration };
