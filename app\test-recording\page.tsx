'use client';

import { useState, useRef } from 'react';
import { VideoRecorder, type RecordingState } from '@/lib/videoRecorder';
import { checkRecordingSupport, getDisplayMessage } from '@/lib/videoRecordingSupport';
import { videoConverter } from '@/lib/videoConverter';

/**
 * 视频录制功能测试页面
 * 用于测试和调试录制功能
 */
export default function TestRecordingPage() {
  const [recordingState, setRecordingState] = useState<RecordingState>({
    status: 'idle',
    progress: 0,
    duration: 0
  });
  const [supportInfo, setSupportInfo] = useState<any>(null);
  const [conversionSupport, setConversionSupport] = useState<any>(null);
  const [logs, setLogs] = useState<string[]>([]);

  const videoRecorderRef = useRef<VideoRecorder | null>(null);
  const testAreaRef = useRef<HTMLDivElement>(null);

  // 添加日志
  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    console.log(`🧪 [测试] ${message}`);
  };

  // 检查录制支持
  const checkSupport = async () => {
    addLog('检查浏览器录制支持...');
    const support = checkRecordingSupport();
    setSupportInfo(support);
    
    const message = getDisplayMessage(support);
    addLog(`录制支持: ${support.supportLevel} - ${message.message}`);

    // 检查服务器转换支持
    addLog('检查服务器转换支持...');
    const conversionInfo = await videoConverter.checkConversionSupport();
    setConversionSupport(conversionInfo);
    addLog(`服务器转换支持: ${conversionInfo.supported ? '是' : '否'}`);
  };

  // 开始录制测试
  const startRecordingTest = async () => {
    if (!testAreaRef.current) {
      addLog('错误: 测试区域未找到');
      return;
    }

    try {
      addLog('开始录制测试...');
      
      // 创建录制器
      if (!videoRecorderRef.current) {
        videoRecorderRef.current = new VideoRecorder({
          width: 1920,
          height: 1080,
          frameRate: 30
        });

        // 监听状态变化
        videoRecorderRef.current.addEventListener('stateChange', (data) => {
          setRecordingState(data.state);
          addLog(`状态变化: ${data.state.status}`);
        });

        videoRecorderRef.current.addEventListener('error', (data) => {
          addLog(`录制错误: ${data.state.error}`);
        });

        videoRecorderRef.current.addEventListener('completed', (data) => {
          addLog('录制完成！');
        });
      }

      // 准备录制
      await videoRecorderRef.current.prepare(testAreaRef.current);
      addLog('录制环境准备完成');

      // 开始录制
      await videoRecorderRef.current.startRecording(3);
      addLog('录制已开始');

    } catch (error) {
      addLog(`录制失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 停止录制
  const stopRecording = async () => {
    if (!videoRecorderRef.current) return;

    try {
      addLog('停止录制...');
      await videoRecorderRef.current.stopRecording();
    } catch (error) {
      addLog(`停止录制失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 下载视频
  const downloadVideo = (format: 'webm' | 'mp4') => {
    if (format === 'mp4' && recordingState.convertedDownloadUrl) {
      const link = document.createElement('a');
      link.href = recordingState.convertedDownloadUrl;
      link.download = `test-recording-${Date.now()}.mp4`;
      link.click();
      addLog('MP4视频下载已开始');
    } else if (format === 'webm' && recordingState.downloadUrl) {
      const link = document.createElement('a');
      link.href = recordingState.downloadUrl;
      link.download = `test-recording-${Date.now()}.webm`;
      link.click();
      addLog('WebM视频下载已开始');
    }
  };

  // 清理资源
  const cleanup = () => {
    if (videoRecorderRef.current) {
      videoRecorderRef.current.cleanup();
      videoRecorderRef.current = null;
      addLog('录制器资源已清理');
    }
    setRecordingState({
      status: 'idle',
      progress: 0,
      duration: 0
    });
  };

  // 清空日志
  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">视频录制功能测试</h1>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：控制面板 */}
          <div className="space-y-6">
            {/* 支持检测 */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">功能支持检测</h2>
              <button
                onClick={checkSupport}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 mb-4"
              >
                检查支持情况
              </button>

              {supportInfo && (
                <div className="space-y-2 text-sm">
                  <div>支持级别: <span className="font-mono">{supportInfo.supportLevel}</span></div>
                  <div>MediaRecorder: {supportInfo.mediaRecorder ? '✅' : '❌'}</div>
                  <div>Canvas捕获: {supportInfo.canvasCapture ? '✅' : '❌'}</div>
                  <div>Web Audio API: {supportInfo.webAudioAPI ? '✅' : '❌'}</div>
                </div>
              )}

              {conversionSupport && (
                <div className="mt-4 pt-4 border-t">
                  <div className="text-sm">
                    服务器转换: {conversionSupport.supported ? '✅' : '❌'}
                  </div>
                </div>
              )}
            </div>

            {/* 录制控制 */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">录制控制</h2>
              
              <div className="space-y-4">
                <div className="flex gap-2">
                  <button
                    onClick={startRecordingTest}
                    disabled={recordingState.status !== 'idle'}
                    className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 disabled:opacity-50"
                  >
                    开始录制
                  </button>
                  
                  <button
                    onClick={stopRecording}
                    disabled={recordingState.status !== 'recording'}
                    className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 disabled:opacity-50"
                  >
                    停止录制
                  </button>
                  
                  <button
                    onClick={cleanup}
                    className="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700"
                  >
                    清理资源
                  </button>
                </div>

                {/* 状态显示 */}
                <div className="p-3 bg-gray-50 rounded">
                  <div>状态: <span className="font-mono">{recordingState.status}</span></div>
                  <div>进度: {recordingState.progress.toFixed(1)}%</div>
                  <div>时长: {recordingState.duration.toFixed(1)}s</div>
                  {recordingState.error && (
                    <div className="text-red-600">错误: {recordingState.error}</div>
                  )}
                </div>

                {/* 下载按钮 */}
                {recordingState.status === 'completed' && (
                  <div className="flex gap-2">
                    {recordingState.convertedDownloadUrl && (
                      <button
                        onClick={() => downloadVideo('mp4')}
                        className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
                      >
                        下载MP4
                      </button>
                    )}
                    {recordingState.downloadUrl && (
                      <button
                        onClick={() => downloadVideo('webm')}
                        className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                      >
                        下载WebM
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 右侧：测试区域和日志 */}
          <div className="space-y-6">
            {/* 测试区域 */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">测试区域</h2>
              <div
                ref={testAreaRef}
                className="w-full h-64 bg-gradient-to-br from-blue-400 to-purple-600 rounded-lg flex items-center justify-center relative overflow-hidden"
              >
                <div className="text-white text-center">
                  <h3 className="text-2xl font-bold mb-2">测试动画区域</h3>
                  <p className="text-lg">时间: {new Date().toLocaleTimeString()}</p>
                  
                  {/* 简单的动画元素 */}
                  <div className="mt-4 flex justify-center space-x-4">
                    <div className="w-4 h-4 bg-white rounded-full animate-bounce" style={{ animationDelay: '0s' }}></div>
                    <div className="w-4 h-4 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-4 h-4 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>

                {/* Canvas测试元素 */}
                <canvas
                  width="100"
                  height="100"
                  className="absolute top-4 right-4 border border-white/30 rounded"
                  ref={(canvas) => {
                    if (canvas) {
                      const ctx = canvas.getContext('2d');
                      if (ctx) {
                        // 简单的Canvas动画
                        let frame = 0;
                        const animate = () => {
                          ctx.clearRect(0, 0, 100, 100);
                          ctx.fillStyle = `hsl(${frame % 360}, 70%, 50%)`;
                          ctx.fillRect(10, 10, 80, 80);
                          frame += 2;
                          requestAnimationFrame(animate);
                        };
                        animate();
                      }
                    }
                  }}
                />
              </div>
            </div>

            {/* 日志区域 */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">测试日志</h2>
                <button
                  onClick={clearLogs}
                  className="text-sm bg-gray-200 text-gray-700 px-3 py-1 rounded hover:bg-gray-300"
                >
                  清空日志
                </button>
              </div>
              
              <div className="h-64 overflow-y-auto bg-gray-50 rounded p-3 font-mono text-sm">
                {logs.length === 0 ? (
                  <div className="text-gray-500">暂无日志...</div>
                ) : (
                  logs.map((log, index) => (
                    <div key={index} className="mb-1">
                      {log}
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
