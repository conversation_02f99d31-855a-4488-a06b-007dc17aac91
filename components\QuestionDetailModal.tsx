import React, { useState } from 'react';
import { Dialog, Tab } from '@headlessui/react';
import AudioPlayer from './AudioPlayer';

interface QuestionDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  data: {
    stem_id: number;
    content: string;
    stem_txt: string | null;
    task_id: string | null;
    raw_response: string | null;
    new_response: string | null;
  };
}

// 定义标记类型
interface Mark {
  text: string;
  type: 'circle' | 'wave';
}

// 定义内容块的类型
interface ContentBlock {
  fl: string;    // 分类
  yw: string;    // 原文
  jb: string;    // 脚本
  url: string;   // 音频URL
  zm?: string;   // 字幕
  mark?: Mark[]; // 标记数组
}

// 定义标签页内容的类型
interface TabContent {
  stem: ContentBlock[];
  analysis: ContentBlock[];
  answer: ContentBlock[];
  summary: ContentBlock[];
}

// 渲染标记内容的组件
const MarkContent = ({ text, marks }: { text: string; marks?: Mark[] }) => {
  if (!marks || marks.length === 0) {
    return <span>{text}</span>;
  }

  let result = text;
  let elements: React.ReactNode[] = [];
  let lastIndex = 0;

  marks.forEach((mark, index) => {
    const markIndex = result.indexOf(mark.text, lastIndex);
    if (markIndex !== -1) {
      // 添加标记前的文本
      if (markIndex > lastIndex) {
        elements.push(result.substring(lastIndex, markIndex));
      }

      // 添加标记的文本
      const markElement = (
        <span
          key={index}
          className={
            mark.type === 'circle'
              ? 'inline-block px-2 py-1 bg-red-100 border-2 border-red-300 rounded-full text-red-700 font-semibold'
              : 'relative inline-block border-b-2 border-blue-400 border-dotted text-blue-700 font-semibold'
          }
        >
          {mark.text}
        </span>
      );
      elements.push(markElement);

      lastIndex = markIndex + mark.text.length;
    }
  });

  // 添加剩余的文本
  if (lastIndex < result.length) {
    elements.push(result.substring(lastIndex));
  }

  return <>{elements}</>;
};

const ContentBlock = ({ block }: { block: ContentBlock }) => (
  <div className="mb-3 border rounded p-3 bg-white">
    {/* 标题和内容在同一行 */}
    <div className="grid grid-cols-2 gap-4">
      {/* 左侧：分类标题和原文 */}
      <div>
        <h4 className="font-bold text-sm text-gray-900 mb-2">{block.fl}</h4>
        <div className="bg-gray-50 p-2 rounded text-sm">
          <p className="text-gray-700">{block.yw}</p>
        </div>
        {/* 显示标记内容（如果存在） */}
        {block.mark && block.mark.length > 0 && (
          <div className="mt-2 p-2 bg-blue-50 rounded text-sm">
            <p className="text-blue-800 font-medium mb-1">标记内容:</p>
            <p className="text-blue-700">
              <MarkContent text={block.yw} marks={block.mark} />
            </p>
          </div>
        )}
      </div>

      {/* 右侧：脚本、字幕和音频 */}
      <div className="space-y-2">
        <div className="bg-gray-50 p-2 rounded text-sm">
          <p className="text-gray-700">{block.jb}</p>
        </div>
        {/* 显示字幕内容（如果存在） */}
        {block.zm && (
          <div className="bg-green-50 p-2 rounded text-sm">
            <p className="text-green-800 font-medium mb-1">字幕:</p>
            <p className="text-green-700">{block.zm}</p>
          </div>
        )}
        <AudioPlayer url={block.url} />
      </div>
    </div>
  </div>
);

// 格式化JSON字符串为带颜色的HTML
const formatJSON = (jsonString: string) => {
  try {
    // 解析JSON字符串
    const obj = JSON.parse(jsonString);
    // 将JSON对象重新格式化为字符串，带缩进
    const formattedStr = JSON.stringify(obj, null, 2);
    
    // 添加语法高亮
    return formattedStr
      // 先处理换行和缩进
      .replace(/\n/g, '<br/>')
      .replace(/\s/g, '&nbsp;')
      // 处理键名（key）- 红色
      .replace(/"([^"]+)"(?=\s*:)/g, '<span class="text-red-500">"$1"</span>')
      // 处理字符串值（value）- 蓝色
      .replace(/:\s*"([^"]+)"/g, ': <span class="text-blue-500">"$1"</span>')
      // 处理结构符号 - 灰色
      .replace(/([{}\[\],])/g, '<span class="text-gray-400">$1</span>');
  } catch (error) {
    console.error('JSON parsing error:', error);
    return jsonString; // 如果解析失败，返回原始字符串
  }
};

export default function QuestionDetailModal({ isOpen, onClose, data }: QuestionDetailModalProps) {
  // 解析JSON数据
  const parseContent = (): TabContent | null => {
    try {
      if (!data.new_response) return null;
      return JSON.parse(data.new_response);
    } catch (error) {
      console.error('Error parsing new_response:', error);
      return null;
    }
  };

  const content = parseContent();

  return (
    <Dialog 
      open={isOpen} 
      onClose={onClose}
      className="relative z-50"
    >
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="relative bg-white rounded-lg w-[95vw] h-[95vh] flex flex-col">
          {/* 标题区域 */}
          <div className="p-3 border-b">
            <Dialog.Title className="text-base font-semibold">
              题目详情 
              <span className="ml-2 text-sm text-gray-500">
                (ID: {data.stem_id} | Task: {data.task_id || '-'})
              </span>
            </Dialog.Title>
          </div>

          {/* 内容区域 - 上半部分 */}
          <div className="grid grid-cols-2 gap-4 p-3 h-[25vh]">
            {/* 左侧：题目内容和题干 */}
            <div className="space-y-2">
              {/* 题目内容 - 如果为空则不显示 */}
              {data.content && (
                <div className="space-y-1 h-1/2">
                  <h3 className="font-medium text-sm text-gray-700">题目内容</h3>
                  <div className="h-[calc(12.5vh-2rem)] overflow-y-auto bg-gray-50 rounded p-3 text-sm">
                    {data.content}
                  </div>
                </div>
              )}
              {/* 题干内容 */}
              <div className={`space-y-1 ${data.content ? 'h-1/2' : 'h-full'}`}>
                <h3 className="font-medium text-sm text-gray-700">题干</h3>
                <div className={`${data.content ? 'h-[calc(12.5vh-2rem)]' : 'h-[calc(25vh-2rem)]'} overflow-y-auto bg-gray-50 rounded p-3 text-sm`}>
                  {data.stem_txt || '暂无题干'}
                </div>
              </div>
            </div>

            {/* 右侧：处理结果 */}
            <div className="space-y-1">
              <h3 className="font-medium text-sm text-gray-700">处理结果</h3>
              <div className="h-full max-h-[calc(25vh-2rem)] overflow-y-auto bg-gray-50 rounded p-3">
                <pre 
                  className="text-xs font-mono"
                  dangerouslySetInnerHTML={{
                    __html: data.new_response ? formatJSON(data.new_response) : '暂无数据'
                  }}
                />
              </div>
            </div>
          </div>

          {/* Tab切换区域 - 下半部分 */}
          <div className="flex-1 p-3 overflow-hidden">
            <Tab.Group>
              <Tab.List className="flex space-x-1 w-fit rounded-lg bg-gray-100 p-1">
                {['读题', '分析', '解析', '总结'].map((category) => (
                  <Tab
                    key={category}
                    className={({ selected }) =>
                      `px-4 py-1.5 text-sm font-medium rounded-md
                      ${selected
                        ? 'bg-white text-blue-600 shadow'
                        : 'text-gray-600 hover:bg-white/[0.12] hover:text-gray-800'
                      }`
                    }
                  >
                    {category}
                  </Tab>
                ))}
              </Tab.List>
              <Tab.Panels className="mt-3 h-[calc(70vh-6rem)] overflow-y-auto">
                <Tab.Panel>
                  {content?.stem?.map((block, index) => (
                    <ContentBlock key={index} block={block} />
                  ))}
                </Tab.Panel>
                <Tab.Panel>
                  {content?.analysis?.map((block, index) => (
                    <ContentBlock key={index} block={block} />
                  ))}
                </Tab.Panel>
                <Tab.Panel>
                  {content?.answer?.map((block, index) => (
                    <ContentBlock key={index} block={block} />
                  ))}
                </Tab.Panel>
                <Tab.Panel>
                  {content?.summary?.map((block, index) => (
                    <ContentBlock key={index} block={block} />
                  ))}
                </Tab.Panel>
              </Tab.Panels>
            </Tab.Group>
          </div>

          {/* 关闭按钮 */}
          <button
            className="absolute top-3 right-3 p-1.5 text-gray-400 hover:text-gray-500"
            onClick={onClose}
          >
            <span className="sr-only">关闭</span>
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
}