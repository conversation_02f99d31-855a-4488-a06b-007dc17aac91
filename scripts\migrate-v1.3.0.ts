const Database = require('better-sqlite3');
const path = require('path');

const DB_PATH = path.join(process.cwd(), 'local.db');

/**
 * 数据库迁移函数 - V1.3.0
 * 添加 stem_txt 字段到 stems 表
 */
function migrate() {
  console.log('🚀 Starting database migration v1.3.0...');
  
  const db = new Database(DB_PATH, { verbose: console.log });

  try {
    // 开始事务
    db.prepare('BEGIN').run();

    // 1. 创建临时表
    db.prepare(`
      CREATE TABLE stems_temp (
        stem_id INTEGER PRIMARY KEY AUTOINCREMENT,
        content TEXT NOT NULL,
        images TEXT,
        stem_txt TEXT
      )
    `).run();

    // 2. 复制现有数据到临时表
    db.prepare(`
      INSERT INTO stems_temp (stem_id, content, images)
      SELECT stem_id, content, images
      FROM stems
    `).run();

    // 3. 创建tasks临时表
    db.prepare(`
      CREATE TABLE tasks_temp (
        task_id TEXT PRIMARY KEY,
        stem_id INTEGER NOT NULL,
        status TEXT NOT NULL,
        raw_response TEXT,
        new_response TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `).run();

    // 4. 复制tasks数据
    db.prepare(`
      INSERT INTO tasks_temp 
      SELECT * FROM tasks
    `).run();

    // 5. 创建videos临时表
    db.prepare(`
      CREATE TABLE videos_temp (
        video_id INTEGER PRIMARY KEY AUTOINCREMENT,
        task_id TEXT NOT NULL,
        scene_data TEXT NOT NULL,
        output_path TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `).run();

    // 6. 复制videos数据
    db.prepare(`
      INSERT INTO videos_temp 
      SELECT * FROM videos
    `).run();

    // 7. 创建audio临时表
    db.prepare(`
      CREATE TABLE audio_temp (
        audio_id INTEGER PRIMARY KEY AUTOINCREMENT,
        audio_name TEXT NOT NULL,
        task_id TEXT NOT NULL,
        url TEXT NOT NULL,
        local_path TEXT NOT NULL,
        status TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `).run();

    // 8. 复制audio数据
    db.prepare(`
      INSERT INTO audio_temp 
      SELECT * FROM audio
    `).run();

    // 9. 按正确顺序删除原表
    db.prepare('DROP TABLE audio').run();
    db.prepare('DROP TABLE videos').run();
    db.prepare('DROP TABLE tasks').run();
    db.prepare('DROP TABLE stems').run();

    // 10. 按正确顺序重命名临时表
    db.prepare('ALTER TABLE stems_temp RENAME TO stems').run();
    db.prepare('ALTER TABLE tasks_temp RENAME TO tasks').run();
    db.prepare('ALTER TABLE videos_temp RENAME TO videos').run();
    db.prepare('ALTER TABLE audio_temp RENAME TO audio').run();

    // 11. 重新添加外键约束
    db.prepare(`
      CREATE TRIGGER fk_tasks_stem_id
      BEFORE INSERT ON tasks
      FOR EACH ROW
      BEGIN
        SELECT CASE
          WHEN NEW.stem_id NOT IN (SELECT stem_id FROM stems)
          THEN RAISE(ROLLBACK, 'Foreign key violation: stem_id not in stems')
        END;
      END;
    `).run();

    db.prepare(`
      CREATE TRIGGER fk_videos_task_id
      BEFORE INSERT ON videos
      FOR EACH ROW
      BEGIN
        SELECT CASE
          WHEN NEW.task_id NOT IN (SELECT task_id FROM tasks)
          THEN RAISE(ROLLBACK, 'Foreign key violation: task_id not in tasks')
        END;
      END;
    `).run();

    db.prepare(`
      CREATE TRIGGER fk_audio_task_id
      BEFORE INSERT ON audio
      FOR EACH ROW
      BEGIN
        SELECT CASE
          WHEN NEW.task_id NOT IN (SELECT task_id FROM tasks)
          THEN RAISE(ROLLBACK, 'Foreign key violation: task_id not in tasks')
        END;
      END;
    `).run();

    // 提交事务
    db.prepare('COMMIT').run();

    console.log('✅ Migration completed successfully');
    
    // 验证迁移结果
    const tableInfo = db.prepare("PRAGMA table_info('stems')").all();
    console.log('\n📊 Updated stems table structure:');
    console.table(tableInfo);

  } catch (error) {
    // 发生错误时回滚
    db.prepare('ROLLBACK').run();
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    db.close();
  }
}

// 运行迁移
migrate(); 