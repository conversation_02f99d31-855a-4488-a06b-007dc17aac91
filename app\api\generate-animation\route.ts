import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { AudioProcessor } from '@/lib/audioProcessor';

export async function POST(request: Request) {
  try {
    const { content, stemTxt, processedJson } = await request.json();
    
    // 验证JSON格式
    let parsedJson;
    try {
      parsedJson = JSON.parse(processedJson);
    } catch (error) {
      return NextResponse.json(
        { error: '处理后JSON格式不正确' },
        { status: 400 }
      );
    }
    
    // 检查题目是否重复
    const existingStem = db.findStemByContent(content);
    if (existingStem) {
      return NextResponse.json({
        duplicate: true,
        stemId: existingStem.stem_id
      });
    }

    // 创建新题目，包含 stem_txt
    const stemId = db.createStem(content, null, stemTxt);
    
    // 生成一个唯一的executeId（模拟Coze API的返回）
    const executeId = `direct_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      // 创建任务记录
      db.createTask(stemId, executeId);

      // 直接更新任务响应，将processedJson作为raw_response
      await db.updateTaskResponse(executeId, processedJson);

      // 获取关联的 stem_id
      const task = await db.getTask(executeId);
      if (task) {
        // 处理音频文件（不使用字符处理）
        await AudioProcessor.processTaskAudio(
          executeId,
          task.stem_id,
          processedJson,
          false  // 不使用字符处理
        );
      }

      return NextResponse.json({
        executeId: executeId,
        stemId: stemId
      });
    } catch (processingError) {
      // 如果处理失败，删除已创建的题目记录
      try {
        db.deleteStem(stemId);
      } catch (dbError) {
        console.error('Failed to delete stem:', dbError);
      }
      throw processingError;
    }
    
  } catch (error) {
    console.error('Generate animation error:', error);

    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Failed to generate animation',
        details: error instanceof Error ? error.cause : undefined,
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}
