interface CozeErrorResponse {
  code: number;
  msg: string;
}

interface FetchOptions {
  method?: string;
  headers?: Record<string, string>;
  body?: any;
  retries?: number;
  delay?: number;
}

export class CozeApiError extends Error {
  code: number;
  
  constructor(code: number, message: string) {
    super(message);
    this.code = code;
    this.name = 'CozeApiError';
  }
}

export async function fetchWithRetry(url: string, options: FetchOptions = {}) {
  const {
    method = 'GET',
    headers = {},
    body,
    retries = 3,
    delay = 1000
  } = options;

  let lastError: Error | null = null;

  for (let i = 0; i < retries; i++) {
    try {
      console.log(`Attempt ${i + 1}: Sending request to ${url}`, {
        method,
        headers: { ...headers, Authorization: '***' },
        body
      });

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        ...(body && { body: JSON.stringify(body) })
      });

      const text = await response.text();
      console.log(`Attempt ${i + 1}: Response received`, {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        body: text
      });
      
      // 尝试解析JSON
      try {
        const data = JSON.parse(text);
        
        // 检查Coze错误响应
        if (data.code && data.msg) {
          const cozeError = data as CozeErrorResponse;
          throw new CozeApiError(cozeError.code, cozeError.msg);
        }
        
        // 检查HTTP状态
        if (!response.ok) {
          throw new Error(`API error: ${response.status} ${response.statusText}`);
        }
        
        return data;
      } catch (e) {
        if (e instanceof CozeApiError) {
          throw e; // 直接抛出Coze错误
        }
        throw new Error(`Invalid JSON response: ${text}`);
      }
    } catch (error) {
      lastError = error as Error;
      
      // 如果是Coze API错误，立即停止重试
      if (error instanceof CozeApiError) {
        throw error;
      }
      
      console.error(`Attempt ${i + 1} failed:`, error);
      
      // 最后一次尝试失败时不需要等待
      if (i < retries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError || new Error('Request failed after retries');
} 