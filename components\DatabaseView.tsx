'use client';

import { useEffect, useState } from 'react';
import QuestionDetailModal from './QuestionDetailModal';
import AnimationModal from './AnimationModal';

interface StemInfo {
  stem_id: number;
  content: string;
  stem_txt: string;
  task_id: string | null;
  status: string | null;
  raw_response: string | null;
  new_response: string | null;
}

export default function DatabaseView() {
  const [stems, setStems] = useState<StemInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedStem, setSelectedStem] = useState<StemInfo | null>(null);
  const [showAnimation, setShowAnimation] = useState<StemInfo | null>(null);

  useEffect(() => {
    const fetchStems = async () => {
      try {
        const response = await fetch('/api/stems');
        if (!response.ok) throw new Error('Failed to fetch stems');
        const data = await response.json();
        setStems(data);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    fetchStems();
  }, []);

  if (loading) return <div className="text-center py-8">加载中...</div>;
  if (error) return <div className="text-red-500 py-8">{error}</div>;

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">题目列表</h2>
      <div className="overflow-x-auto bg-white rounded-lg shadow">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="w-[10%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                题目ID
              </th>
              <th className="w-[45%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                内容
              </th>
              <th className="w-[20%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                任务ID
              </th>
              <th className="w-[10%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                状态
              </th>
              <th className="w-[15%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {stems.map((stem) => (
              <tr key={stem.stem_id} className="hover:bg-gray-50">
                <td className="px-4 py-3 text-sm font-medium text-gray-900">
                  {stem.stem_id}
                </td>
                <td className="px-4 py-3 text-sm text-gray-500">
                  <div className="max-h-[4.5em] overflow-y-auto pr-2 line-clamp-3">
                    {stem.content}
                  </div>
                </td>
                <td className="px-4 py-3 text-sm text-gray-500">
                  <div className="max-w-[200px] truncate">
                    {stem.task_id || '-'}
                  </div>
                </td>
                <td className="px-4 py-3">
                  <span className={`inline-flex px-2 py-1 text-xs rounded-full ${getStatusColor(stem.status)}`}>
                    {stem.status || '未开始'}
                  </span>
                </td>
                <td className="px-4 py-3 text-sm">
                  <div className="flex flex-col space-y-2">
                    <button
                      onClick={() => setSelectedStem(stem)}
                      className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1.5 rounded text-sm transition-colors w-full"
                    >
                      查看详情
                    </button>
                    <button
                      onClick={() => setShowAnimation(stem)}
                      className="bg-black hover:bg-gray-800 text-white px-3 py-1.5 rounded text-sm transition-colors w-full"
                    >
                      查看动画
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {selectedStem && (
        <QuestionDetailModal
          isOpen={!!selectedStem}
          onClose={() => setSelectedStem(null)}
          data={selectedStem}
        />
      )}

      {showAnimation && (
        <AnimationModal
          isOpen={!!showAnimation}
          onClose={() => setShowAnimation(null)}
          data={showAnimation}
        />
      )}
    </div>
  );
}

function getStatusColor(status: string | null): string {
  switch (status) {
    case 'waiting':
      return 'bg-yellow-100 text-yellow-800';
    case 'polling':
      return 'bg-blue-100 text-blue-800';
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'failed':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
} 