# AI讲题生产平台

## 项目概述

这是一个基于Next.js开发的AI讲题生产平台，通过集成Coze API实现智能题目解析和讲解。

### 技术栈
- **前端框架**: Next.js 14 + TypeScript + Tailwind CSS
- **状态管理**: Zustand
- **API集成**: Coze API异步工作流
- **数据存储**: SQLite + 本地文件系统
- **UI组件**: Headless UI
- **数学公式**: KaTeX

### 代码结构

```
project/
├── app/                        # Next.js应用目录
│   ├── api/                   # API路由
│   │   ├── submit/           # 任务提交API
│   │   ├── status/           # 状态查询API
│   │   ├── stems/            # 题目列表API 
│   │   └── audio/            # 音频文件API 
│   ├── layout.tsx            # 全局布局
│   ├── globals.css           # 全局样式定义
│   └── page.tsx              # 主页面
│
├── components/                # React组件
│   ├── layout/               # 布局组件
│   │   └── Header.tsx       # 页面头部组件
│   ├── AudioPlayer.tsx       # 音频播放器
│   ├── SerialAudioPlayer.tsx # 串联音频播放器
│   ├── DatabaseView.tsx      # 数据库内容展示
│   ├── QuestionDetailModal.tsx # 题目详情弹窗
│   ├── QuestionContent.tsx   # 题目内容展示
│   ├── AnimationModal.tsx    # 动画展示弹窗
│   ├── WavyUnderline.tsx    # 波浪线绘制
│   ├── SubmitForm.tsx        # 任务提交表单
│   └── TaskList.tsx          # 任务列表
│
├── lib/                      # 工具库
│   ├── api.ts               # API请求工具
│   ├── audio.ts             # 音频处理工具
│   ├── audioProcessor.ts    # 音频处理器
│   ├── db.ts               # 数据库操作
│   ├── latex.ts            # LaTeX渲染工具
│   └── store.ts            # 状态管理工具 
│
├── config/                   # 配置文件
│   └── database.ts          # 数据库配置
│
├── scripts/                  # 脚本文件 
│   ├── init-db.ts          # 数据库初始化脚本
│   ├── clear-db.ts         # 数据库清理脚本
│   ├── show-schema.ts      # 数据库结构查看脚本
│   └── migrate-v1.3.0.ts   # 数据库迁移脚本
│
├── store/                    # 状态管理
│   └── taskStore.ts         # 任务状态管理
│
├── types/                    # TypeScript类型定义
│   ├── database.ts          # 数据库类型
│   └── task.ts              # 任务类型
│
├── storage/                  # 文件存储
│   └── [stem_id]/           # 按题目ID归类
│       ├── audio/           # 音频文件
│       └── images/          # 图片文件
│
├── .gitignore               # Git忽略配置
├── package.json             # 项目依赖配置
├── README.md                # 项目说明文档
└── VERSION.md               # 版本更新历史 
```

### 组件说明

1. **页面组件**
   - `page.tsx`: 主页面布局，整合所有功能模块
   - `layout.tsx`: 全局布局，包含样式和字体配置

2. **功能组件**
   - `SubmitForm`: 任务创建表单，处理题目提交
   - `TaskList`: 任务列表展示，包含轮询逻辑
   - `DatabaseView`: 数据库内容展示，支持查看详情
   - `QuestionDetailModal`: 题目详情弹窗，支持多媒体展示
   - `AnimationModal`: 动画展示弹窗，支持音频播放控制
   - `SerialAudioPlayer`: 串联音频播放器，支持进度控制，蒙层
   - `QuestionContent`: 题目内容展示，支持分区布局
   - `WavyUnderline`: 波浪线绘制

3. **工具模块**
   - `audio.ts`: 音频URL提取、下载和路径转换
   - `audioProcessor.ts`: 音频处理流程控制
   - `db.ts`: 数据库CRUD操作封装
   - `latex.ts`: LaTeX公式渲染工具

4. **状态管理**
   - `taskStore.ts`: 使用Zustand管理任务状态
   - 支持任务创建、更新和状态追踪

### API路由

1. **题目管理**
   
   a. **提交新题目**
   ```typescript
   POST /api/submit
   
   // 请求体
   {
     content: string;    // 题目内容
     stemTxt?: string;   // 题目纯文本(可选)
   }
   
   // 成功响应
   {
     executeId: string;  // 任务执行ID
     stemId: number;     // 题目ID
   }
   
   // 重复题目响应
   {
     duplicate: true;
     stemId: number;     // 已存在的题目ID
   }
   ```

   b. **获取题目列表**
   ```typescript
   GET /api/stems
   
   // 响应
   {
     stems: Array<{
       stem_id: number;      // 题目ID
       content: string;      // 题目内容
       stem_txt: string;     // 题目纯文本
       task_id: string;      // 关联任务ID
       status: string;       // 任务状态
       raw_response: string; // 原始响应
       new_response: string; // 处理后响应
     }>
   }
   ```

   c. **查询任务状态**
   ```typescript
   GET /api/status/[executeId]
   
   // 路径参数
   executeId: string;    // 任务执行ID
   
   // 响应
   {
     status: 'waiting' | 'polling' | 'completed' | 'failed';
     result?: any;       // 任务结果(如果完成)
     lastPollTime: string; // 最后轮询时间
   }
   ```

   d. **获取本地音频**
   ```typescript
   GET /api/audio
   
   // 查询参数
   ?taskId: string;      // 任务ID
   ?audioName: string;   // 音频文件名
   
   // 响应
   Binary Audio File     // 音频文件二进制流
   ```

2. **数据处理**

   a. **音频文件处理**
   - 支持音频URL提取和下载
   - 本地文件系统存储
   - 音频状态跟踪
   ```typescript
   // 音频状态枚举
   type AudioStatus = 'pending' | 'downloading' | 'completed' | 'failed';
   ```

   b. **状态更新**
   - 任务状态自动轮询
   - 首次20秒后开始轮询
   - 之后每60秒轮询一次
   - 最多轮询3次
   ```typescript
   // 任务状态枚举
   type TaskStatus = 'waiting' | 'polling' | 'completed' | 'failed';
   ```

   c. **错误处理**
   ```typescript
   // 错误响应格式
   {
     error: string;      // 错误信息
     code?: number;      // 错误代码(可选)
     details?: any;      // 详细信息(可选)
   }
   ```

3. **数据库操作**

   a. **题目操作**
   ```typescript
   // 查找题目
   findStemByContent(content: string): { stem_id: number } | undefined
   
   // 创建题目
   createStem(
     content: string, 
     images?: string | null, 
     stemTxt?: string | null
   ): number  // 返回stem_id
   ```

   b. **任务操作**
   ```typescript
   // 创建任务
   createTask(stemId: number, taskId: string): void
   
   // 更新任务响应
   updateTaskResponse(
     taskId: string, 
     rawResponse: string
   ): void
   
   // 获取任务信息
   getTask(taskId: string): Task | undefined
   ```

### 数据库设计

#### 1. 题目表 (stems)
```sql
CREATE TABLE stems (
    stem_id INTEGER PRIMARY KEY AUTOINCREMENT,  -- 题目ID
    content TEXT NOT NULL,                      -- 题目内容
    images TEXT,                                -- 题目图片路径(JSON数组)
    stem_txt TEXT                               -- 题目纯文本内容
);
```

#### 2. 任务表 (tasks)
```sql
CREATE TABLE tasks (
    task_id TEXT PRIMARY KEY,                   -- 任务ID (execute_id)
    stem_id INTEGER NOT NULL,                   -- 关联题目ID
    status TEXT NOT NULL,                       -- 任务状态
    raw_response TEXT,                          -- 原始JSON响应
    new_response TEXT,                          -- 处理后的JSON响应(含本地音频路径)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (stem_id) REFERENCES stems(stem_id)
);
```

#### 3. 视频表 (videos)
```sql
CREATE TABLE videos (
    video_id INTEGER PRIMARY KEY AUTOINCREMENT, -- 视频ID
    task_id TEXT NOT NULL,                      -- 关联任务ID
    scene_data TEXT NOT NULL,                   -- 场景数据(JSON)
    output_path TEXT,                           -- 输出视频路径
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(task_id)
);
```

#### 4. 音频表 (audio)
```sql
CREATE TABLE audio (
    audio_id INTEGER PRIMARY KEY AUTOINCREMENT, -- 音频ID
    audio_name TEXT NOT NULL,                   -- 音频名称
    task_id TEXT NOT NULL,                      -- 关联任务ID
    url TEXT NOT NULL,                          -- 音频URL
    local_path TEXT NOT NULL,                   -- 本地存储路径
    status TEXT NOT NULL,                       -- 音频状态
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(task_id)
);