'use client'

import { useState } from 'react'
import { useTaskStore } from '@/store/taskStore'

interface ErrorResponse {
  error: string;
  code?: number;
}

export default function SubmitForm() {
  const [content, setContent] = useState('')
  const [stemTxt, setStemTxt] = useState('')
  const [processedJson, setProcessedJson] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isGeneratingAnimation, setIsGeneratingAnimation] = useState(false)
  const [duplicateInfo, setDuplicateInfo] = useState<{stemId: number} | null>(null)
  const { addTask } = useTaskStore()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!content.trim() || !stemTxt.trim() || isSubmitting) return

    setIsSubmitting(true)
    try {
      const response = await fetch('/api/submit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content,
          stemTxt
        })
      })

      const data = await response.json()

      if (!response.ok) {
        const errorData = data as ErrorResponse
        throw new Error(errorData.code
          ? `Coze API错误 (${errorData.code}): ${errorData.error}`
          : errorData.error
        )
      }

      if (data.duplicate) {
        setDuplicateInfo({ stemId: data.stemId })
      } else {
        addTask(content, data.executeId)
        setContent('')
        setStemTxt('')
        setProcessedJson('')
        setDuplicateInfo(null)
      }
    } catch (error) {
      console.error('提交错误:', error)
      alert(error instanceof Error ? error.message : '提交失败,请重试')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleGenerateAnimation = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!stemTxt.trim() || !processedJson.trim() || isGeneratingAnimation) return

    // 验证JSON格式
    try {
      JSON.parse(processedJson)
    } catch (error) {
      alert('处理后JSON格式不正确，请检查')
      return
    }

    setIsGeneratingAnimation(true)
    try {
      const response = await fetch('/api/generate-animation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: stemTxt,
          stemTxt,
          processedJson
        })
      })

      const data = await response.json()

      if (!response.ok) {
        const errorData = data as ErrorResponse
        throw new Error(errorData.error)
      }

      if (data.duplicate) {
        setDuplicateInfo({ stemId: data.stemId })
      } else {
        addTask(stemTxt, data.executeId)
        setContent('')
        setStemTxt('')
        setProcessedJson('')
        setDuplicateInfo(null)
      }
    } catch (error) {
      console.error('生成动画错误:', error)
      alert(error instanceof Error ? error.message : '生成动画失败,请重试')
    } finally {
      setIsGeneratingAnimation(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 原有的新建任务表单 */}
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-4">
          <div>
            <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-1">
              题目内容
            </label>
            <textarea
              id="content"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="请输入完整的题目内容..."
              className="w-full h-32 p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isSubmitting}
            />
          </div>
          <div>
            <label htmlFor="stemTxt" className="block text-sm font-medium text-gray-700 mb-1">
              题干
            </label>
            <textarea
              id="stemTxt"
              value={stemTxt}
              onChange={(e) => setStemTxt(e.target.value)}
              placeholder="请输入题干部分..."
              className="w-full h-32 p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isSubmitting}
            />
          </div>
        </div>

        {duplicateInfo && (
          <div className="text-yellow-600 bg-yellow-50 p-3 rounded-lg">
            题目已存在，题目ID为：{duplicateInfo.stemId}
          </div>
        )}

        <button
          type="submit"
          disabled={isSubmitting || !content.trim() || !stemTxt.trim()}
          className="w-full py-2 px-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          {isSubmitting ? '提交中...' : '新建任务'}
        </button>
      </form>

      {/* 分隔线 */}
      <div className="border-t border-gray-200 pt-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">直接生成动画</h3>

        {/* 直接生成动画表单 */}
        <form onSubmit={handleGenerateAnimation} className="space-y-4">
          <div>
            <label htmlFor="processedJson" className="block text-sm font-medium text-gray-700 mb-1">
              处理后JSON
            </label>
            <textarea
              id="processedJson"
              value={processedJson}
              onChange={(e) => setProcessedJson(e.target.value)}
              placeholder="请输入处理后的JSON数据..."
              className="w-full h-40 p-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent font-mono text-sm"
              disabled={isGeneratingAnimation}
            />
          </div>

          <button
            type="submit"
            disabled={isGeneratingAnimation || !stemTxt.trim() || !processedJson.trim()}
            className="w-full py-2 px-4 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {isGeneratingAnimation ? '生成中...' : '直接生成动画'}
          </button>
        </form>
      </div>
    </div>
  )
}