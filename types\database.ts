// 题目表接口
export interface Stem {
    stem_id: number;
    content: string;
    images: string | null;  // JSON字符串
}

// 任务表接口
export interface Task {
    task_id: string;
    stem_id: number;
    status: string;
    raw_response: string | null;
    new_response: string | null;
    created_at: string;
    updated_at: string;
}

// 视频表接口
export interface Video {
    video_id: number;
    task_id: string;
    scene_data: string;
    output_path: string | null;
    created_at: string;
}

// 音频表接口
export interface Audio {
    audio_id: number;
    audio_name: string;
    task_id: string;
    url: string;
    local_path: string;
    status: string;
    duration?: number; // 音频时长（秒）
    created_at: string;
}