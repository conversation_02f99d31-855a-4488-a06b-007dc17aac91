import { NextRequest, NextResponse } from 'next/server';
import { writeFile, unlink, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// 注意：这里假设已经安装了 fluent-ffmpeg 和 ffmpeg-static
// 如果没有安装，需要先运行: npm install fluent-ffmpeg ffmpeg-static @types/fluent-ffmpeg

let ffmpeg: any;
let ffmpegPath: string;

try {
  ffmpeg = require('fluent-ffmpeg');
  ffmpegPath = require('ffmpeg-static');
  if (ffmpegPath) {
    ffmpeg.setFfmpegPath(ffmpegPath);
  }
} catch (error) {
  console.warn('FFmpeg dependencies not installed. Video conversion will not be available.');
}

/**
 * 视频格式转换API
 * POST /api/video/convert
 * 
 * 接收WebM格式的视频文件，转换为MP4格式
 */
export async function POST(request: NextRequest) {
  if (!ffmpeg || !ffmpegPath) {
    return NextResponse.json(
      { error: 'FFmpeg not available. Please install fluent-ffmpeg and ffmpeg-static.' },
      { status: 500 }
    );
  }

  try {
    // 解析表单数据
    const formData = await request.formData();
    const videoFile = formData.get('video') as File;
    
    if (!videoFile) {
      return NextResponse.json(
        { error: 'No video file provided' },
        { status: 400 }
      );
    }

    // 验证文件类型
    if (!videoFile.type.includes('webm') && !videoFile.type.includes('mp4')) {
      return NextResponse.json(
        { error: 'Invalid file type. Only WebM and MP4 files are supported.' },
        { status: 400 }
      );
    }

    // 生成唯一的文件ID
    const fileId = uuidv4();
    const tempDir = path.join(process.cwd(), 'temp', 'video');
    
    // 确保临时目录存在
    if (!existsSync(tempDir)) {
      await mkdir(tempDir, { recursive: true });
    }

    // 定义文件路径
    const inputPath = path.join(tempDir, `${fileId}_input.webm`);
    const outputPath = path.join(tempDir, `${fileId}_output.mp4`);

    try {
      // 保存上传的文件
      const arrayBuffer = await videoFile.arrayBuffer();
      await writeFile(inputPath, Buffer.from(arrayBuffer));

      console.log('🎬 开始视频转换:', {
        inputFile: inputPath,
        outputFile: outputPath,
        originalSize: videoFile.size
      });

      // 执行视频转换
      await convertVideo(inputPath, outputPath);

      // 读取转换后的文件
      const convertedBuffer = await import('fs/promises').then(fs => fs.readFile(outputPath));

      console.log('🎬 视频转换完成:', {
        originalSize: videoFile.size,
        convertedSize: convertedBuffer.length
      });

      // 清理临时文件
      await cleanupFiles([inputPath, outputPath]);

      // 返回转换后的视频文件
      return new NextResponse(convertedBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'video/mp4',
          'Content-Disposition': `attachment; filename="animation-${Date.now()}.mp4"`,
          'Content-Length': convertedBuffer.length.toString(),
        },
      });

    } catch (conversionError) {
      // 清理可能存在的临时文件
      await cleanupFiles([inputPath, outputPath]);
      throw conversionError;
    }

  } catch (error) {
    console.error('🎬 视频转换失败:', error);
    
    return NextResponse.json(
      { 
        error: 'Video conversion failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * 执行视频转换
 */
function convertVideo(inputPath: string, outputPath: string): Promise<void> {
  return new Promise((resolve, reject) => {
    ffmpeg(inputPath)
      .outputOptions([
        '-c:v libx264',           // 使用H.264编码器
        '-preset fast',           // 快速编码预设
        '-crf 23',               // 恒定质量因子 (18-28, 23是默认值)
        '-c:a aac',              // 音频编码器
        '-b:a 128k',             // 音频比特率
        '-movflags +faststart',   // 优化网络播放
        '-pix_fmt yuv420p',      // 像素格式，确保兼容性
        '-r 30',                 // 帧率
        '-s 1920x1080'           // 分辨率
      ])
      .on('start', (commandLine) => {
        console.log('🎬 FFmpeg命令:', commandLine);
      })
      .on('progress', (progress) => {
        console.log('🎬 转换进度:', Math.round(progress.percent || 0) + '%');
      })
      .on('end', () => {
        console.log('🎬 视频转换完成');
        resolve();
      })
      .on('error', (error) => {
        console.error('🎬 FFmpeg转换错误:', error);
        reject(new Error(`Video conversion failed: ${error.message}`));
      })
      .save(outputPath);
  });
}

/**
 * 清理临时文件
 */
async function cleanupFiles(filePaths: string[]): Promise<void> {
  const cleanupPromises = filePaths.map(async (filePath) => {
    try {
      if (existsSync(filePath)) {
        await unlink(filePath);
        console.log('🗑️ 清理临时文件:', filePath);
      }
    } catch (error) {
      console.warn('🗑️ 清理文件失败:', filePath, error);
    }
  });

  await Promise.all(cleanupPromises);
}

/**
 * 获取视频转换状态API
 * GET /api/video/convert?status=true
 */
export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const checkStatus = url.searchParams.get('status');

  if (checkStatus) {
    return NextResponse.json({
      ffmpegAvailable: !!(ffmpeg && ffmpegPath),
      supportedFormats: {
        input: ['webm', 'mp4'],
        output: ['mp4']
      },
      maxFileSize: '100MB', // 可以根据需要调整
      features: {
        h264Encoding: true,
        aacAudio: true,
        fastStart: true,
        qualityControl: true
      }
    });
  }

  return NextResponse.json(
    { error: 'Invalid request' },
    { status: 400 }
  );
}
