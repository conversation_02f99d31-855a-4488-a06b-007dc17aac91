import { NextRequest, NextResponse } from 'next/server';
import path from 'path';
import fs from 'fs/promises';

export async function GET(request: NextRequest) {
  try {
    const audioPath = request.nextUrl.searchParams.get('path');
    if (!audioPath) {
      return new NextResponse('Missing path parameter', { status: 400 });
    }

    // 移除开头的斜杠（如果存在）
    const normalizedPath = audioPath.startsWith('/') ? audioPath.slice(1) : audioPath;
    
    // 构建绝对路径
    const absolutePath = path.join(process.cwd(), normalizedPath);

    // 安全检查：确保路径在storage目录内
    const storagePath = path.join(process.cwd(), 'storage');
    if (!path.normalize(absolutePath).startsWith(storagePath)) {
      return new NextResponse('Invalid path', { status: 403 });
    }

    // 读取音频文件
    const audioFile = await fs.readFile(absolutePath);

    return new NextResponse(audioFile, {
      headers: {
        'Content-Type': 'audio/mpeg',
        'Content-Length': audioFile.length.toString(),
      },
    });
  } catch (error) {
    console.error('[API] Error serving audio file:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
} 