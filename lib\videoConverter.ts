/**
 * 客户端视频转换工具
 * 处理视频上传和格式转换
 */

export interface ConversionProgress {
  status: 'uploading' | 'converting' | 'downloading' | 'completed' | 'error';
  progress: number;
  message: string;
  error?: string;
}

export type ConversionProgressCallback = (progress: ConversionProgress) => void;

/**
 * 视频转换器类
 */
export class VideoConverter {
  private baseUrl: string;

  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl;
  }

  /**
   * 检查服务器转换功能状态
   */
  async checkConversionSupport(): Promise<{
    supported: boolean;
    features?: any;
    error?: string;
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/api/video/convert?status=true`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        supported: data.ffmpegAvailable,
        features: data
      };

    } catch (error) {
      console.error('检查转换支持失败:', error);
      return {
        supported: false,
        error: error instanceof Error ? error.message : '检查转换支持失败'
      };
    }
  }

  /**
   * 转换视频格式
   * @param videoBlob 原始视频Blob (WebM格式)
   * @param onProgress 进度回调函数
   * @returns Promise<Blob> 转换后的MP4视频Blob
   */
  async convertToMP4(
    videoBlob: Blob, 
    onProgress?: ConversionProgressCallback
  ): Promise<Blob> {
    
    const updateProgress = (progress: ConversionProgress) => {
      if (onProgress) {
        onProgress(progress);
      }
    };

    try {
      // 检查输入
      if (!videoBlob || videoBlob.size === 0) {
        throw new Error('无效的视频文件');
      }

      updateProgress({
        status: 'uploading',
        progress: 0,
        message: '准备上传视频...'
      });

      // 创建表单数据
      const formData = new FormData();
      formData.append('video', videoBlob, 'recording.webm');

      updateProgress({
        status: 'uploading',
        progress: 10,
        message: '上传视频文件...'
      });

      // 上传并转换
      const response = await this.uploadWithProgress(formData, updateProgress);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `转换失败: HTTP ${response.status}`);
      }

      updateProgress({
        status: 'downloading',
        progress: 90,
        message: '下载转换后的视频...'
      });

      // 获取转换后的视频
      const convertedBlob = await response.blob();

      updateProgress({
        status: 'completed',
        progress: 100,
        message: '转换完成！'
      });

      console.log('🎬 视频转换成功:', {
        originalSize: videoBlob.size,
        convertedSize: convertedBlob.size,
        originalType: videoBlob.type,
        convertedType: convertedBlob.type
      });

      return convertedBlob;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '转换失败';
      
      updateProgress({
        status: 'error',
        progress: 0,
        message: '转换失败',
        error: errorMessage
      });

      console.error('🎬 视频转换失败:', error);
      throw error;
    }
  }

  /**
   * 带进度的文件上传
   */
  private async uploadWithProgress(
    formData: FormData,
    onProgress: ConversionProgressCallback
  ): Promise<Response> {
    
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      // 上传进度监听
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const uploadProgress = Math.round((event.loaded / event.total) * 70); // 上传占70%
          onProgress({
            status: 'uploading',
            progress: Math.min(uploadProgress, 70),
            message: `上传中... ${uploadProgress}%`
          });
        }
      });

      // 响应处理
      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          onProgress({
            status: 'converting',
            progress: 80,
            message: '服务器处理中...'
          });

          // 创建Response对象
          const response = new Response(xhr.response, {
            status: xhr.status,
            statusText: xhr.statusText,
            headers: new Headers(xhr.getAllResponseHeaders().split('\r\n').reduce((headers, line) => {
              const [key, value] = line.split(': ');
              if (key && value) {
                headers[key] = value;
              }
              return headers;
            }, {} as Record<string, string>))
          });

          resolve(response);
        } else {
          reject(new Error(`Upload failed: ${xhr.status} ${xhr.statusText}`));
        }
      });

      // 错误处理
      xhr.addEventListener('error', () => {
        reject(new Error('网络错误'));
      });

      xhr.addEventListener('timeout', () => {
        reject(new Error('上传超时'));
      });

      // 配置请求
      xhr.open('POST', `${this.baseUrl}/api/video/convert`);
      xhr.timeout = 300000; // 5分钟超时
      xhr.responseType = 'blob';

      // 发送请求
      xhr.send(formData);
    });
  }

  /**
   * 下载视频文件
   */
  static downloadVideo(blob: Blob, filename?: string): void {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    
    link.href = url;
    link.download = filename || `animation-${Date.now()}.mp4`;
    
    // 添加到DOM并触发下载
    document.body.appendChild(link);
    link.click();
    
    // 清理
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  /**
   * 获取视频信息
   */
  static async getVideoInfo(blob: Blob): Promise<{
    duration: number;
    size: number;
    type: string;
  }> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      const url = URL.createObjectURL(blob);

      video.addEventListener('loadedmetadata', () => {
        const info = {
          duration: video.duration,
          size: blob.size,
          type: blob.type
        };
        
        URL.revokeObjectURL(url);
        resolve(info);
      });

      video.addEventListener('error', (error) => {
        URL.revokeObjectURL(url);
        reject(new Error('无法读取视频信息'));
      });

      video.src = url;
    });
  }

  /**
   * 验证视频文件
   */
  static validateVideoFile(blob: Blob): {
    valid: boolean;
    error?: string;
  } {
    // 检查文件大小 (最大100MB)
    const maxSize = 100 * 1024 * 1024;
    if (blob.size > maxSize) {
      return {
        valid: false,
        error: '视频文件过大，最大支持100MB'
      };
    }

    // 检查文件类型
    const supportedTypes = ['video/webm', 'video/mp4'];
    if (!supportedTypes.some(type => blob.type.includes(type))) {
      return {
        valid: false,
        error: '不支持的视频格式，仅支持WebM和MP4'
      };
    }

    return { valid: true };
  }
}

// 创建默认实例
export const videoConverter = new VideoConverter();
