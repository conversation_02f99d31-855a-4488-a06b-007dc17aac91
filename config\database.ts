import Database from 'better-sqlite3';
import path from 'path';

// 数据库配置
export const dbConfig = {
    path: path.join(process.cwd(), 'local.db'),
    options: {
        verbose: console.log,
        fileMustExist: true
    }
};

/**
 * 创建数据库连接
 */
export function createConnection() {
    return new Database(dbConfig.path, dbConfig.options);
}

// 数据库操作
export const dbOperations = {
    // ... 现有操作保持不变 ...

    /**
     * 插入音频记录
     */
    insertAudio: (audioName: string, taskId: string, url: string, localPath: string, status: string) => {
        const db = createConnection();
        try {
            const stmt = db.prepare(`
                INSERT INTO audio (audio_name, task_id, url, local_path, status)
                VALUES (?, ?, ?, ?, ?)
            `);
            return stmt.run(audioName, taskId, url, localPath, status);
        } finally {
            db.close();
        }
    },

    /**
     * 更新音频状态
     */
    updateAudioStatus: (taskId: string, audioName: string, status: string) => {
        const db = createConnection();
        try {
            const stmt = db.prepare(`
                UPDATE audio 
                SET status = ? 
                WHERE task_id = ? AND audio_name = ?
            `);
            return stmt.run(status, taskId, audioName);
        } finally {
            db.close();
        }
    },

    /**
     * 获取任务的所有音频
     */
    getTaskAudios: (taskId: string) => {
        const db = createConnection();
        try {
            const stmt = db.prepare(`
                SELECT * FROM audio 
                WHERE task_id = ? 
                ORDER BY audio_name
            `);
            return stmt.all(taskId);
        } finally {
            db.close();
        }
    },

    /**
     * 删除任务的所有音频记录
     */
    deleteTaskAudios: (taskId: string) => {
        const db = createConnection();
        try {
            const stmt = db.prepare(`
                DELETE FROM audio 
                WHERE task_id = ?
            `);
            return stmt.run(taskId);
        } finally {
            db.close();
        }
    }
};