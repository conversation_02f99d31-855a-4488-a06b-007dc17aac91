import fs from 'fs/promises';
import path from 'path';
import { db } from './db';
import { createConnection } from '@/config/database';
import { audioDurationUtils } from './audioDuration';

interface Task {
    task_id: string;
    stem_id: number;
    status: string;
    raw_response: string;
    new_response?: string;
}

export const audioUtils = {
  // 下载音频文件
  async downloadAudio(url: string, taskId: string, index: number): Promise<string> {
    console.log(`[Audio] Downloading #${index} for task ${taskId}`);

    const task = await db.getTaskById(taskId) as Task;
    if (!task) throw new Error('Task not found');

    // 构建相对路径
    const relativePath = `storage/${task.stem_id}/audio/${index}.mp3`;
    const absolutePath = path.join(process.cwd(), relativePath);

    try {
      await fs.mkdir(path.dirname(absolutePath), { recursive: true });
      
      const response = await fetch(url);
      const buffer = await response.arrayBuffer();
      await fs.writeFile(absolutePath, Buffer.from(buffer));

      // 计算音频时长
      console.log(`[Audio] 计算音频时长: ${relativePath}`);
      const duration = await audioDurationUtils.getMP3Duration(relativePath);

      await db.createAudio({
        audio_name: `${index}.mp3`,
        task_id: taskId,
        url: url,
        local_path: relativePath,  // 存储相对路径
        status: 'pending',
        duration: duration
      });

      return relativePath;  // 返回相对路径
    } catch (error) {
      console.error(`[Audio] Download failed for ${url}:`, error);
      throw error;
    }
  },

  // 提取并处理音频URL
  async processAudioUrls(rawResponse: string, taskId: string, useProcessing: boolean = true): Promise<void> {
    try {
      const urlPattern = /https:\/\/[^"\\]*\.mp3/g;
      const urls = rawResponse.match(urlPattern) || [];
      const uniqueUrls = [...new Set(urls)];

      if (uniqueUrls.length === 0) return;

      console.log(`[Audio] Processing ${uniqueUrls.length} audio files for task ${taskId}`);
      const urlMapping = new Map<string, string>();
      
      for (let i = 0; i < uniqueUrls.length; i++) {
        try {
          const localPath = await this.downloadAudio(uniqueUrls[i], taskId, i + 1);
          const relativePath = localPath.replace(process.cwd(), '').replace(/\\/g, '/');
          urlMapping.set(uniqueUrls[i], relativePath);
        } catch (error) {
          console.error(`[Audio] Processing failed for audio #${i + 1}`);
        }
      }

      if (urlMapping.size > 0) {
        let newResponse = rawResponse;
        urlMapping.forEach((localPath, url) => {
          newResponse = newResponse.replace(
            new RegExp(url.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), 
            localPath
          );
        });

        // 根据参数决定是否使用字符处理
        if (useProcessing) {
          await db.updateNewResponse(taskId, newResponse);
        } else {
          await db.updateNewResponseDirect(taskId, newResponse);
        }
        console.log(`[Audio] Task ${taskId} processing completed`);
      }

    } catch (error) {
      console.error('[Audio] Processing failed:', error);
      throw error;
    }
  }
}; 