/**
 * 视频录制功能支持检测工具
 * 检查浏览器对MediaRecorder、Canvas捕获等API的兼容性
 */

export interface RecordingSupportInfo {
  // 基础API支持
  mediaRecorder: boolean;
  canvasCapture: boolean;
  webAudioAPI: boolean;
  
  // 视频格式支持
  webmSupport: boolean;
  mp4Support: boolean;
  h264Support: boolean;
  
  // 音频格式支持
  audioWebmSupport: boolean;
  audioMp4Support: boolean;
  
  // 高级功能支持
  requestAnimationFrame: boolean;
  offscreenCanvas: boolean;
  
  // 整体兼容性评估
  isFullySupported: boolean;
  supportLevel: 'excellent' | 'good' | 'limited' | 'unsupported';
  
  // 不支持的功能列表
  unsupportedFeatures: string[];
  
  // 建议信息
  recommendations: string[];
}

/**
 * 检测浏览器录制功能支持情况
 */
export const checkRecordingSupport = (): RecordingSupportInfo => {
  const support: RecordingSupportInfo = {
    mediaRecorder: false,
    canvasCapture: false,
    webAudioAPI: false,
    webmSupport: false,
    mp4Support: false,
    h264Support: false,
    audioWebmSupport: false,
    audioMp4Support: false,
    requestAnimationFrame: false,
    offscreenCanvas: false,
    isFullySupported: false,
    supportLevel: 'unsupported',
    unsupportedFeatures: [],
    recommendations: []
  };

  // 检测基础API
  support.mediaRecorder = typeof MediaRecorder !== 'undefined';
  support.canvasCapture = !!(HTMLCanvasElement.prototype.captureStream);
  support.webAudioAPI = !!(window.AudioContext || (window as any).webkitAudioContext);
  support.requestAnimationFrame = typeof requestAnimationFrame !== 'undefined';
  
  // 检测OffscreenCanvas支持
  support.offscreenCanvas = typeof OffscreenCanvas !== 'undefined';

  // 检测视频格式支持
  if (support.mediaRecorder) {
    try {
      support.webmSupport = MediaRecorder.isTypeSupported('video/webm');
      support.mp4Support = MediaRecorder.isTypeSupported('video/mp4');
      support.h264Support = MediaRecorder.isTypeSupported('video/webm;codecs=h264');
      
      // 检测音频格式支持
      support.audioWebmSupport = MediaRecorder.isTypeSupported('audio/webm');
      support.audioMp4Support = MediaRecorder.isTypeSupported('audio/mp4');
    } catch (error) {
      console.warn('Error checking MediaRecorder format support:', error);
    }
  }

  // 收集不支持的功能
  const featureChecks = [
    { key: 'mediaRecorder', name: 'MediaRecorder API' },
    { key: 'canvasCapture', name: 'Canvas Stream Capture' },
    { key: 'webAudioAPI', name: 'Web Audio API' },
    { key: 'requestAnimationFrame', name: 'RequestAnimationFrame' },
    { key: 'webmSupport', name: 'WebM Video Format' }
  ];

  featureChecks.forEach(({ key, name }) => {
    if (!support[key as keyof RecordingSupportInfo]) {
      support.unsupportedFeatures.push(name);
    }
  });

  // 评估整体兼容性
  const coreFeatures = [
    support.mediaRecorder,
    support.canvasCapture,
    support.webAudioAPI,
    support.requestAnimationFrame
  ];

  const coreSupported = coreFeatures.filter(Boolean).length;
  const formatSupported = support.webmSupport || support.mp4Support;

  if (coreSupported === 4 && formatSupported) {
    support.isFullySupported = true;
    support.supportLevel = 'excellent';
  } else if (coreSupported >= 3 && formatSupported) {
    support.supportLevel = 'good';
  } else if (coreSupported >= 2) {
    support.supportLevel = 'limited';
  } else {
    support.supportLevel = 'unsupported';
  }

  // 生成建议
  if (support.supportLevel === 'unsupported') {
    support.recommendations.push('请使用现代浏览器（Chrome 51+、Firefox 43+、Safari 14.1+、Edge 79+）');
  } else if (support.supportLevel === 'limited') {
    support.recommendations.push('部分功能可能不可用，建议升级浏览器版本');
  } else if (!support.offscreenCanvas) {
    support.recommendations.push('建议使用支持OffscreenCanvas的浏览器以获得更好的性能');
  }

  if (!support.webmSupport && !support.mp4Support) {
    support.recommendations.push('浏览器不支持视频录制格式，无法进行录制');
  }

  return support;
};

/**
 * 获取推荐的录制配置
 */
export const getRecommendedRecordingConfig = (support: RecordingSupportInfo) => {
  const config: {
    mimeType: string;
    videoBitsPerSecond?: number;
    audioBitsPerSecond?: number;
  } = {
    mimeType: 'video/webm'
  };

  // 选择最佳的MIME类型
  if (support.h264Support) {
    config.mimeType = 'video/webm;codecs=h264';
  } else if (support.webmSupport) {
    config.mimeType = 'video/webm';
  } else if (support.mp4Support) {
    config.mimeType = 'video/mp4';
  }

  // 根据支持情况设置比特率
  if (support.supportLevel === 'excellent') {
    config.videoBitsPerSecond = 8000000; // 8Mbps for 1080p
    config.audioBitsPerSecond = 128000;  // 128kbps
  } else if (support.supportLevel === 'good') {
    config.videoBitsPerSecond = 5000000; // 5Mbps
    config.audioBitsPerSecond = 96000;   // 96kbps
  } else {
    config.videoBitsPerSecond = 2500000; // 2.5Mbps
    config.audioBitsPerSecond = 64000;   // 64kbps
  }

  return config;
};

/**
 * 显示用户友好的支持信息
 */
export const getDisplayMessage = (support: RecordingSupportInfo): {
  title: string;
  message: string;
  type: 'success' | 'warning' | 'error';
} => {
  switch (support.supportLevel) {
    case 'excellent':
      return {
        title: '录制功能完全支持',
        message: '您的浏览器完全支持高质量视频录制功能！',
        type: 'success'
      };
    
    case 'good':
      return {
        title: '录制功能良好支持',
        message: '您的浏览器支持视频录制，可能有轻微的功能限制。',
        type: 'success'
      };
    
    case 'limited':
      return {
        title: '录制功能有限支持',
        message: `部分录制功能不可用。不支持的功能：${support.unsupportedFeatures.join('、')}`,
        type: 'warning'
      };
    
    case 'unsupported':
      return {
        title: '不支持录制功能',
        message: '您的浏览器不支持视频录制功能，请升级到现代浏览器。',
        type: 'error'
      };
  }
};

/**
 * 在控制台输出详细的支持信息（用于调试）
 */
export const logSupportInfo = (support: RecordingSupportInfo) => {
  console.group('🎥 视频录制功能支持检测');
  
  console.log('📊 整体评估:', {
    支持级别: support.supportLevel,
    完全支持: support.isFullySupported ? '✅' : '❌'
  });

  console.log('🔧 基础API支持:', {
    MediaRecorder: support.mediaRecorder ? '✅' : '❌',
    Canvas捕获: support.canvasCapture ? '✅' : '❌',
    WebAudio: support.webAudioAPI ? '✅' : '❌',
    动画帧: support.requestAnimationFrame ? '✅' : '❌',
    离屏Canvas: support.offscreenCanvas ? '✅' : '❌'
  });

  console.log('🎬 格式支持:', {
    WebM视频: support.webmSupport ? '✅' : '❌',
    MP4视频: support.mp4Support ? '✅' : '❌',
    H264编码: support.h264Support ? '✅' : '❌',
    WebM音频: support.audioWebmSupport ? '✅' : '❌',
    MP4音频: support.audioMp4Support ? '✅' : '❌'
  });

  if (support.unsupportedFeatures.length > 0) {
    console.warn('⚠️ 不支持的功能:', support.unsupportedFeatures);
  }

  if (support.recommendations.length > 0) {
    console.info('💡 建议:', support.recommendations);
  }

  console.groupEnd();
};
