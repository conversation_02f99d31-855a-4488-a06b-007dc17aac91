{"name": "app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0 -p 3000", "build": "next build", "start": "next start -H 0.0.0.0 -p 3000", "lint": "next lint", "init-db": "ts-node scripts/init-db.ts", "migrate": "ts-node scripts/migrate-v1.3.0.ts"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "axios": "^1.7.9", "better-sqlite3": "^8.7.0", "katex": "^0.16.19", "markdown-it": "^14.1.0", "mp3-duration": "^1.1.0", "music-metadata": "^11.7.3", "next": "15.1.0", "node-ffprobe": "^3.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-katex": "^3.0.1", "react-markdown": "^9.0.1", "rehype-katex": "^7.0.1", "remark-math": "^6.0.0", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/typography": "^0.5.15", "@types/better-sqlite3": "^7.6.12", "@types/katex": "^0.16.7", "@types/markdown-it": "^14.1.2", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^9", "eslint-config-next": "15.1.0", "postcss": "^8", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5"}}