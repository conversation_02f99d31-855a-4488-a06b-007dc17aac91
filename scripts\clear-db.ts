const Database = require('better-sqlite3');
const db = new Database('local.db');

function clearDatabase() {
  try {
    // 开始事务
    db.prepare('BEGIN').run();

    // 清空所有表
    db.prepare('DELETE FROM audios').run();
    db.prepare('DELETE FROM videos').run();
    db.prepare('DELETE FROM tasks').run();
    db.prepare('DELETE FROM stems').run();

    // 重置自增ID
    db.prepare('DELETE FROM sqlite_sequence').run();

    // 提交事务
    db.prepare('COMMIT').run();

    console.log('数据库清空成功');
  } catch (error) {
    // 发生错误时回滚
    db.prepare('ROLLBACK').run();
    console.error('清空数据库失败:', error);
  } finally {
    db.close();
  }
}

// 运行清空操作
clearDatabase(); 