import { create } from 'zustand';
import { Task } from '@/types/task';

// 定义状态管理接口
interface TaskStore {
  lastTaskId: number;
  tasks: Task[];
  addTask: (content: string, executeId: string) => void;
  updateTask: (executeId: string, updates: Partial<Task>) => void;
}

// 创建状态管理
export const useTaskStore = create<TaskStore>((set) => ({
  lastTaskId: 10000000,
  tasks: [],
  
  // 添加新任务
  addTask: (content, executeId) => set((state) => ({
    lastTaskId: state.lastTaskId + 1,
    tasks: [{
      taskId: state.lastTaskId + 1,
      executeId,
      content,
      status: 'waiting',
      pollCount: 0,
      createdAt: new Date()
    }, ...state.tasks]
  })),
  
  // 更新任务状态
  updateTask: (executeId, updates) => set((state) => ({
    tasks: state.tasks.map(task => 
      task.executeId === executeId 
        ? { ...task, ...updates }
        : task
    )
  }))
})); 