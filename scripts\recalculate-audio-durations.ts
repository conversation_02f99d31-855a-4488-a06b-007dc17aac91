#!/usr/bin/env tsx

import { createConnection } from '../config/database';
import { audioDurationUtils } from '../lib/audioDuration';
import { db } from '../lib/db';
import path from 'path';

interface AudioRecord {
  audio_id: number;
  audio_name: string;
  task_id: string;
  url: string;
  local_path: string;
  status: string;
  duration: number | null;
  created_at: string;
}

async function recalculateAudioDurations() {
  console.log('🔄 开始重新计算所有音频文件的精确时长...');
  
  const database = createConnection();
  
  try {
    // 获取所有音频记录
    const audioRecords = database.prepare(`
      SELECT * FROM audio 
      WHERE local_path IS NOT NULL 
      ORDER BY created_at DESC
    `).all() as AudioRecord[];
    
    console.log(`📊 找到 ${audioRecords.length} 个音频记录`);
    
    if (audioRecords.length === 0) {
      console.log('✅ 没有需要处理的音频记录');
      return;
    }
    
    const updates: Array<{audioId: number, duration: number}> = [];
    let successCount = 0;
    let errorCount = 0;
    
    for (let i = 0; i < audioRecords.length; i++) {
      const record = audioRecords[i];
      const progress = `[${i + 1}/${audioRecords.length}]`;
      
      try {
        console.log(`${progress} 处理音频: ${record.audio_name} (${record.local_path})`);
        
        // 检查文件是否存在
        const absolutePath = path.isAbsolute(record.local_path) 
          ? record.local_path 
          : path.join(process.cwd(), record.local_path);
        
        // 使用新的精确时长计算方法
        const newDuration = await audioDurationUtils.getMP3Duration(record.local_path);
        
        // 比较新旧时长
        const oldDuration = record.duration;
        const difference = oldDuration ? Math.abs(newDuration - oldDuration) : null;
        
        if (difference !== null && difference > 0.1) {
          console.log(`  📈 时长变化: ${oldDuration}秒 -> ${newDuration}秒 (差异: ${difference.toFixed(2)}秒)`);
        } else if (oldDuration === null) {
          console.log(`  ✨ 新增时长: ${newDuration}秒`);
        } else {
          console.log(`  ✓ 时长确认: ${newDuration}秒 (无显著变化)`);
        }
        
        updates.push({
          audioId: record.audio_id,
          duration: newDuration
        });
        
        successCount++;
        
      } catch (error) {
        console.error(`  ❌ 处理失败: ${error instanceof Error ? error.message : '未知错误'}`);
        errorCount++;
      }
    }
    
    // 批量更新数据库
    if (updates.length > 0) {
      console.log(`\n💾 批量更新数据库中的 ${updates.length} 条记录...`);
      await db.batchUpdateAudioDurations(updates);
      console.log('✅ 数据库更新完成');
    }
    
    // 输出统计信息
    console.log('\n📊 处理统计:');
    console.log(`  ✅ 成功: ${successCount} 个文件`);
    console.log(`  ❌ 失败: ${errorCount} 个文件`);
    console.log(`  💾 更新: ${updates.length} 条数据库记录`);
    
    if (successCount > 0) {
      console.log('\n🎉 音频时长重新计算完成！现在所有时长都是基于精确解析的结果。');
    }
    
  } catch (error) {
    console.error('❌ 重新计算过程中发生错误:', error);
    throw error;
  } finally {
    database.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  recalculateAudioDurations()
    .then(() => {
      console.log('🏁 脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 脚本执行失败:', error);
      process.exit(1);
    });
}

export { recalculateAudioDurations };
