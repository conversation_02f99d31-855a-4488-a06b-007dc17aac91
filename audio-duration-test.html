
<!DOCTYPE html>
<html>
<head>
    <title>音频时长测试</title>
</head>
<body>
    <h1>音频时长测试</h1>
    <audio id="testAudio" controls>
        <source src="/api/audio?path=storage%2F88%2Faudio%2F1.mp3" type="audio/mpeg">
        您的浏览器不支持音频元素。
    </audio>
    <div id="result"></div>
    
    <script>
        const audio = document.getElementById('testAudio');
        const result = document.getElementById('result');
        
        audio.addEventListener('loadedmetadata', function() {
            const duration = audio.duration;
            result.innerHTML = `
                <h2>音频信息:</h2>
                <p>实际时长: ${duration.toFixed(2)} 秒</p>
                <p>文件路径: /api/audio?path=storage%2F88%2Faudio%2F1.mp3</p>
            `;
            console.log('实际音频时长:', duration, '秒');
        });
        
        audio.addEventListener('error', function(e) {
            result.innerHTML = '<p style="color: red;">加载音频失败: ' + e.message + '</p>';
            console.error('音频加载错误:', e);
        });
    </script>
</body>
</html>
  