import Database from 'better-sqlite3';
import path from 'path';

const db = new Database(path.join(process.cwd(), 'local.db'));

try {
  // 开启外键支持
  db.pragma('foreign_keys = ON');
  
  // 开始事务
  db.prepare('BEGIN').run();

  // 检查表是否存在
  const tables = db.prepare(`
    SELECT name FROM sqlite_master 
    WHERE type='table' AND name IN ('tasks', 'videos', 'audio')
  `).all();

  const existingTables = new Set(tables.map(t => t.name));

  // 1. 重命名现有表
  if (existingTables.has('tasks')) {
    db.prepare('ALTER TABLE tasks RENAME TO tasks_old').run();
  }
  if (existingTables.has('videos')) {
    db.prepare('ALTER TABLE videos RENAME TO videos_old').run();
  }
  if (existingTables.has('audio')) {
    db.prepare('ALTER TABLE audio RENAME TO audio_old').run();
  }

  // 2. 创建新表（带外键约束）
  db.prepare(`
    CREATE TABLE IF NOT EXISTS tasks (
      task_id TEXT PRIMARY KEY,
      stem_id INTEGER NOT NULL,
      status TEXT NOT NULL,
      raw_response TEXT,
      new_response TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (stem_id) REFERENCES stems(stem_id)
    )
  `).run();

  db.prepare(`
    CREATE TABLE IF NOT EXISTS videos (
      video_id INTEGER PRIMARY KEY AUTOINCREMENT,
      task_id TEXT NOT NULL,
      scene_data TEXT NOT NULL,
      output_path TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (task_id) REFERENCES tasks(task_id)
    )
  `).run();

  db.prepare(`
    CREATE TABLE IF NOT EXISTS audio (
      audio_id INTEGER PRIMARY KEY AUTOINCREMENT,
      audio_name TEXT NOT NULL,
      task_id TEXT NOT NULL,
      url TEXT NOT NULL,
      local_path TEXT NOT NULL,
      status TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (task_id) REFERENCES tasks(task_id)
    )
  `).run();

  // 3. 迁移数据（如果有旧表）
  if (existingTables.has('tasks')) {
    db.prepare(`
      INSERT INTO tasks 
      SELECT task_id, stem_id, status, raw_response, new_response, created_at, updated_at 
      FROM tasks_old
    `).run();
    db.prepare('DROP TABLE tasks_old').run();
  }

  if (existingTables.has('videos')) {
    db.prepare(`
      INSERT INTO videos 
      SELECT video_id, task_id, scene_data, output_path, created_at 
      FROM videos_old
    `).run();
    db.prepare('DROP TABLE videos_old').run();
  }

  if (existingTables.has('audio')) {
    db.prepare(`
      INSERT INTO audio 
      SELECT audio_id, audio_name, task_id, url, local_path, status, created_at 
      FROM audio_old
    `).run();
    db.prepare('DROP TABLE audio_old').run();
  }

  // 提交事务
  db.prepare('COMMIT').run();

  console.log('成功添加外键约束！');

} catch (error) {
  // 回滚事务
  db.prepare('ROLLBACK').run();
  console.error('迁移失败:', error);
  throw error;
} finally {
  db.close();
} 