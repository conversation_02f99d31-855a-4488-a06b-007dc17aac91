import fs from 'fs/promises';
import path from 'path';
import { parseFile } from 'music-metadata';

/**
 * 获取音频文件时长的工具函数
 */
export const audioDurationUtils = {
  /**
   * 精确获取MP3文件时长（使用music-metadata库）
   */
  async getMP3Duration(filePath: string): Promise<number> {
    try {
      const absolutePath = path.isAbsolute(filePath)
        ? filePath
        : path.join(process.cwd(), filePath);

      // 检查文件是否存在
      try {
        await fs.access(absolutePath);
      } catch {
        console.error(`[AudioDuration] 文件不存在: ${absolutePath}`);
        throw new Error(`音频文件不存在: ${filePath}`);
      }

      // 使用music-metadata库精确解析音频元数据
      const metadata = await parseFile(absolutePath);

      if (!metadata.format.duration) {
        throw new Error('无法获取音频时长信息');
      }

      const duration = Math.round(metadata.format.duration * 100) / 100; // 保留两位小数
      console.log(`[AudioDuration] 精确解析音频时长: ${filePath} -> ${duration}秒 (比特率: ${metadata.format.bitrate}kbps, 采样率: ${metadata.format.sampleRate}Hz)`);
      return duration;

    } catch (error) {
      console.error(`[AudioDuration] 获取音频时长失败: ${filePath}`, error);
      throw error;
    }
  },

  /**
   * 使用Web Audio API获取音频时长（在有浏览器环境时使用）
   * 这个方法更准确，但只能在浏览器环境中使用
   */
  async getAudioDurationFromURL(url: string): Promise<number> {
    return new Promise((resolve, reject) => {
      const audio = new Audio();

      audio.addEventListener('loadedmetadata', () => {
        const duration = Math.round(audio.duration * 100) / 100; // 保留两位小数
        console.log(`[AudioDuration] 浏览器获取音频时长: ${url} -> ${duration}秒`);
        resolve(duration);
      });

      audio.addEventListener('error', (error) => {
        console.error(`[AudioDuration] 加载音频元数据失败: ${url}`, error);
        reject(new Error(`无法加载音频元数据: ${url}`));
      });

      // 设置超时
      setTimeout(() => {
        console.error(`[AudioDuration] 获取音频时长超时: ${url}`);
        reject(new Error(`获取音频时长超时: ${url}`));
      }, 10000); // 10秒超时

      audio.src = url;
    });
  },

  /**
   * 批量获取音频时长
   */
  async getBatchDurations(filePaths: string[]): Promise<{[path: string]: number | null}> {
    const results: {[path: string]: number | null} = {};

    console.log(`[AudioDuration] 开始批量处理 ${filePaths.length} 个音频文件`);

    for (const filePath of filePaths) {
      try {
        results[filePath] = await this.getMP3Duration(filePath);
      } catch (error) {
        console.error(`[AudioDuration] 批量处理失败: ${filePath}`, error);
        // 不再使用默认值，记录错误但继续处理其他文件
        results[filePath] = null;
      }
    }

    const successCount = Object.values(results).filter(v => v !== null).length;
    console.log(`[AudioDuration] 批量处理完成: ${successCount}/${filePaths.length} 成功`);

    return results;
  }
};
