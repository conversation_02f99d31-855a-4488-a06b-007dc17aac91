import { createConnection } from '@/config/database';
import { Stem, Task, Audio } from '@/types/database';

export const db = {
  /**
   * 题目查重
   */
  findStemByContent: (content: string) => {
    const db = createConnection();
    try {
      return db.prepare('SELECT stem_id FROM stems WHERE content = ?').get(content);
    } finally {
      db.close();
    }
  },

  /**
   * 创建新题目
   */
  createStem: (content: string, images: string | null = null, stemTxt: string | null = null) => {
    const db = createConnection();
    try {
      const result = db.prepare(
        'INSERT INTO stems (content, images, stem_txt) VALUES (?, ?, ?)'
      ).run(content, images, stemTxt);
      return result.lastInsertRowid;
    } finally {
      db.close();
    }
  },

  /**
   * 创建任务
   */
  createTask: (stemId: number, taskId: string) => {
    const db = createConnection();
    try {
      return db.prepare(
        'INSERT INTO tasks (task_id, stem_id, status) VALUES (?, ?, ?)'
      ).run(taskId, stemId, 'waiting');
    } finally {
      db.close();
    }
  },

  /**
   * 更新任务状态和响应
   */
  updateTaskResponse: (taskId: string, rawResponse: string) => {
    const db = createConnection();
    try {
      return db.prepare(
        'UPDATE tasks SET raw_response = ?, status = ? WHERE task_id = ?'
      ).run(rawResponse, 'completed', taskId);
    } finally {
      db.close();
    }
  },

  /**
   * 获取所有题目信息
   */
  getAllStems: () => {
    const db = createConnection();
    try {
      return db.prepare(`
        SELECT 
          s.stem_id,
          s.content,
          s.stem_txt,
          t.task_id,
          t.status,
          t.raw_response,
          t.new_response
        FROM stems s
        LEFT JOIN tasks t ON s.stem_id = t.stem_id
        ORDER BY s.stem_id DESC
      `).all();
    } finally {
      db.close();
    }
  },

  /**
   * 删除题目
   */
  deleteStem: (stemId: number) => {
    const db = createConnection();
    try {
      return db.prepare('DELETE FROM stems WHERE stem_id = ?').run(stemId);
    } finally {
      db.close();
    }
  },

  /**
   * 获取任务信息
   */
  getTask: async (taskId: string) => {
    const db = createConnection();
    try {
      return db.prepare(`
        SELECT 
          task_id,
          stem_id,
          status,
          raw_response,
          new_response
        FROM tasks
        WHERE task_id = ?
      `).get(taskId);
    } finally {
      db.close();
    }
  },

  /**
   * 更新任务的 new_response（带字符处理）
   */
  updateNewResponse: (taskId: string, newResponse: string) => {
    console.log('\n[Database] Updating new_response');
    
    // 预处理响应数据
    let processedResponse = newResponse;
    
    // 第一轮处理：基础字符替换
    for (let i = 0; i < 3; i++) {
      processedResponse = processedResponse.replace(/\\\\/g, '\\');
    }
    processedResponse = processedResponse.replace(/\\"/g, '"')
                                      .replace(/\\n/g, '')
                                      .replace(/"",/g, '');
    
    // 第二轮处理：JSON结构优化
    processedResponse = processedResponse
      // 1. 移除多余的Output字段
      .replace(/{"Output":"/g, '')
      
      // 2-5. 标准化JSON字段格式
      .replace(/"analysis":"/g, '"analysis":')
      .replace(/","answer":"/g, ',"answer":')
      .replace(/","stem":"/g, ',"stem":')
      .replace(/","summary":"/g, ',"summary":')
      
      // 6. 移除末尾多余的 "}
      .replace(/"}$/g, '');

    // 第三轮处理：转义反斜杠
    processedResponse = processedResponse.replace(/\\/g, '\\\\');

    console.log('Processed response length:', processedResponse.length);

    const db = createConnection();
    try {
      const result = db.prepare(
        'UPDATE tasks SET new_response = ? WHERE task_id = ?'
      ).run(processedResponse, taskId);
      
      return result;
    } catch (error) {
      console.error('[Database] Error updating new_response:', error);
      throw error;
    } finally {
      db.close();
    }
  },

  /**
   * 直接更新任务的 new_response（不进行字符处理）
   */
  updateNewResponseDirect: (taskId: string, newResponse: string) => {
    console.log('\n[Database] Updating new_response directly (no processing)');

    const db = createConnection();
    try {
      const result = db.prepare(
        'UPDATE tasks SET new_response = ? WHERE task_id = ?'
      ).run(newResponse, taskId);

      return result;
    } catch (error) {
      console.error('[Database] Error updating new_response directly:', error);
      throw error;
    } finally {
      db.close();
    }
  },

  /**
   * 根据ID获取任务信息
   */
  getTaskById: (taskId: string) => {
    const db = createConnection();
    try {
      return db.prepare(`
        SELECT 
          t.*,
          s.stem_id
        FROM tasks t
        JOIN stems s ON t.stem_id = s.stem_id
        WHERE t.task_id = ?
      `).get(taskId);
    } catch (error) {
      console.error('[Database] Error getting task:', error);
      throw error;
    } finally {
      db.close();
    }
  },

  /**
   * 创建音频记录
   */
  createAudio: (audio: {
    audio_name: string;
    task_id: string;
    url: string;
    local_path?: string;
    status: 'pending' | 'success' | 'failed';
    duration?: number;
  }) => {
    const db = createConnection();
    try {
      console.log('[Database] Creating audio record:', audio);
      // 确保存储相对路径
      const localPath = audio.local_path?.replace(/\\/g, '/'); // 统一使用正斜杠
      const result = db.prepare(`
        INSERT INTO audio (
          audio_name,
          task_id,
          url,
          local_path,
          status,
          duration
        ) VALUES (?, ?, ?, ?, ?, ?)
      `).run(
        audio.audio_name,
        audio.task_id,
        audio.url,
        localPath || null,
        audio.status,
        audio.duration || null
      );
      console.log('[Database] Audio record created successfully');
      return result;
    } catch (error) {
      console.error('[Database] Error creating audio record:', error);
      throw error;
    } finally {
      db.close();
    }
  },

  /**
   * 更新音频状态
   */
  updateAudioStatus: (taskId: string, audioName: string, status: string) => {
    const db = createConnection();
    try {
      console.log('[Database] Updating audio status:', { taskId, audioName, status });
      const result = db.prepare(`
        UPDATE audio
        SET status = ?
        WHERE task_id = ? AND audio_name = ?
      `).run(status, taskId, audioName);
      console.log('[Database] Audio status updated successfully');
      return result;
    } catch (error) {
      console.error('[Database] Error updating audio status:', error);
      throw error;
    } finally {
      db.close();
    }
  },

  /**
   * 获取任务的所有音频
   */
  getTaskAudios: (taskId: string) => {
    const db = createConnection();
    try {
      return db.prepare(`
        SELECT *
        FROM audio
        WHERE task_id = ?
        ORDER BY audio_name
      `).all(taskId);
    } finally {
      db.close();
    }
  },

  /**
   * 根据stem_id获取相关任务
   */
  getTasksByStemId: (stemId: number) => {
    const db = createConnection();
    try {
      return db.prepare(`
        SELECT *
        FROM tasks
        WHERE stem_id = ?
        ORDER BY created_at DESC
      `).all(stemId);
    } finally {
      db.close();
    }
  },

  /**
   * 更新音频时长
   */
  updateAudioDuration: (audioId: number, duration: number) => {
    const db = createConnection();
    try {
      console.log('[Database] Updating audio duration:', { audioId, duration });
      const result = db.prepare(`
        UPDATE audio
        SET duration = ?
        WHERE audio_id = ?
      `).run(duration, audioId);
      console.log('[Database] Audio duration updated successfully');
      return result;
    } catch (error) {
      console.error('[Database] Error updating audio duration:', error);
      throw error;
    } finally {
      db.close();
    }
  },

  /**
   * 批量更新音频时长
   */
  batchUpdateAudioDurations: (updates: Array<{audioId: number, duration: number}>) => {
    const db = createConnection();
    try {
      console.log('[Database] Batch updating audio durations:', updates.length);

      const updateStmt = db.prepare(`
        UPDATE audio
        SET duration = ?
        WHERE audio_id = ?
      `);

      const transaction = db.transaction((updates: Array<{audioId: number, duration: number}>) => {
        for (const update of updates) {
          updateStmt.run(update.duration, update.audioId);
        }
      });

      transaction(updates);
      console.log('[Database] Batch audio duration update completed');
      return { changes: updates.length };
    } catch (error) {
      console.error('[Database] Error in batch updating audio durations:', error);
      throw error;
    } finally {
      db.close();
    }
  }
};