import { NextResponse } from 'next/server';
import { db } from '@/lib/db';

export async function GET(
  request: Request,
  { params }: { params: { stemId: string } }
) {
  try {
    const stemId = parseInt(params.stemId);
    if (isNaN(stemId)) {
      return NextResponse.json({ error: 'Invalid stem ID' }, { status: 400 });
    }

    console.log('[API] Fetching stem details for ID:', stemId);
    const details = await db.getStemDetails(stemId);
    
    if (!details) {
      console.log('[API] No details found for stem ID:', stemId);
      return NextResponse.json({ error: 'Stem not found' }, { status: 404 });
    }

    console.log('[API] Successfully fetched stem details');
    return NextResponse.json(details);
  } catch (error) {
    console.error('[API] Error fetching stem details:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 