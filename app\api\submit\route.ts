import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { fetchWithRetry, CozeApiError } from '@/lib/api';

export async function POST(request: Request) {
  try {
    const { content, stemTxt } = await request.json();
    
    // 检查题目是否重复
    const existingStem = db.findStemByContent(content);
    if (existingStem) {
      return NextResponse.json({
        duplicate: true,
        stemId: existingStem.stem_id
      });
    }

    // 创建新题目，包含 stem_txt
    const stemId = db.createStem(content, null, stemTxt);
    
    // 调用Coze API
    const apiToken = process.env.COZE_API_TOKEN;
    const workflowId = process.env.COZE_WORKFLOW_ID;
    
    if (!apiToken || !workflowId) {
      throw new Error('Missing required environment variables');
    }

    try {
      const data = await fetchWithRetry('https://api.coze.cn/v1/workflow/run', {
        method: 'POST',
        headers: {
          'Authorization': `Bear<PERSON> ${apiToken}`,
          'Accept': 'application/json'
        },
        body: {
          workflow_id: workflowId,
          parameters: {
            INPUT: content,
          },
          is_async: true
        }
      });

      console.log('API Response Data:', JSON.stringify(data, null, 2));

      // 检查不同可能的响应结构
      const executeId = data.execute_id || data.data?.execute_id || data.result?.execute_id;
      
      if (!executeId) {
        console.error('Invalid API response structure:', data);
        throw new Error('Could not find execute_id in response');
      }

      // 创建任务记录
      db.createTask(stemId, executeId);

      return NextResponse.json({
        executeId: executeId,
        stemId: stemId
      });
    } catch (apiError) {
      // 如果API调用失败，删除已创建的题目记录
      try {
        db.deleteStem(stemId);
      } catch (dbError) {
        console.error('Failed to delete stem:', dbError);
      }
      throw apiError;
    }
    
  } catch (error) {
    console.error('Submit error:', error);

    // 处理Coze API错误
    if (error instanceof CozeApiError) {
      return NextResponse.json(
        { 
          error: error.message,
          code: error.code
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Failed to submit task',
        details: error instanceof Error ? error.cause : undefined,
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
} 