'use client';

import { useEffect, useRef, useState } from 'react';
import { PlayIcon, PauseIcon } from '@heroicons/react/24/solid';

interface AudioInfo {
  url: string;
  filename: string;
  section: 'stem' | 'analysis' | 'answer' | 'summary';
  sectionIndex: number;
  duration?: number;
  isLoaded: boolean;
}

interface SerialAudioPlayerProps {
  audioFiles: AudioInfo[];
  isLoading: boolean;
  autoPlayOnLoad?: boolean;
  onAudioChange?: (filename: string) => void;
}

interface SectionSegment {
  section: 'stem' | 'analysis' | 'answer' | 'summary';
  width: number;
  position: number;
  label: string;
  audioIndex: number;
  isMajorBreak: boolean;
}

export default function SerialAudioPlayer({ 
  audioFiles, 
  isLoading,
  autoPlayOnLoad,
  onAudioChange 
}: SerialAudioPlayerProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [totalDuration, setTotalDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const audioRefs = useRef<(HTMLAudioElement | null)[]>([]);

  // 初始化音频元素引用数组
  useEffect(() => {
    audioRefs.current = audioFiles.map(() => null);
  }, [audioFiles.length]);

  // 计算总时长（使用数据库中的时长信息）
  useEffect(() => {
    if (audioFiles.length > 0) {
      // 检查是否所有音频都有时长信息
      const allHaveDuration = audioFiles.every(audio => audio.duration);
      if (allHaveDuration) {
        const total = audioFiles.reduce((sum, audio) => {
          return sum + audio.duration!; // 确保有时长信息
        }, 0);
        setTotalDuration(total);
        console.log('[SerialAudioPlayer] 总时长计算完成:', total, '秒 (基于数据库时长)');
      } else {
        console.warn('[SerialAudioPlayer] 等待所有音频时长信息加载完成');
        setTotalDuration(0); // 重置为0，等待完整信息
      }
    }
  }, [audioFiles]);

  // 处理音频结束事件
  const handleAudioEnd = () => {
    if (currentIndex < audioFiles.length - 1) {
      setCurrentIndex(prev => prev + 1);
      const nextAudio = audioRefs.current[currentIndex + 1];
      if (nextAudio) {
        nextAudio.play();
      }
    } else {
      setIsPlaying(false);
      setCurrentIndex(0);
    }
  };

  // 处理播放/暂停
  const togglePlay = () => {
    const currentAudio = audioRefs.current[currentIndex];
    if (!currentAudio) return;

    if (isPlaying) {
      currentAudio.pause();
    } else {
      currentAudio.play();
    }
    setIsPlaying(!isPlaying);
  };

  // 更新当前播放时间
  const updateCurrentTime = () => {
    let time = 0;
    for (let i = 0; i < currentIndex; i++) {
      const audio = audioRefs.current[i];
      if (audio) {
        time += audio.duration;
      }
    }
    const currentAudio = audioRefs.current[currentIndex];
    if (currentAudio) {
      time += currentAudio.currentTime;
    }
    setCurrentTime(time);
  };

  // 计算所有段落的位置和宽度信息
  const calculateSegments = () => {
    const segments: SectionSegment[] = [];
    const sections = ['stem', 'analysis', 'answer', 'summary'] as const;
    let currentPosition = 0;
    let audioIndex = 0;

    sections.forEach(section => {
      const sectionAudios = audioFiles.filter(audio => audio.section === section);
      if (sectionAudios.length === 0) return;

      // 计算该部分的总时长
      const sectionDuration = sectionAudios.reduce((sum, audio) => 
        sum + (audio.duration || 0), 0);
      const sectionWidth = (sectionDuration / totalDuration) * 100;

      // 为每个音频添加段落信息
      sectionAudios.forEach((audio, index) => {
        const width = audio.duration ? (audio.duration / totalDuration) * 100 : 0;
        
        segments.push({
          section,
          width,
          position: currentPosition,
          label: getSectionLabel(section),
          audioIndex,
          isMajorBreak: index === 0 && currentPosition > 0 // 主要分隔线在每个部分开始处
        });

        currentPosition += width;
        audioIndex++;
      });
    });

    return segments;
  };

  const getSectionLabel = (section: string): string => {
    const labels = {
      stem: '读题',
      analysis: '分析',
      answer: '详解',
      summary: '总结'
    };
    return labels[section as keyof typeof labels];
  };

  // 处理点击进度条
  const handleProgressClick = (index: number) => {
    // 停止当前播放
    const currentAudio = audioRefs.current[currentIndex];
    if (currentAudio) {
      currentAudio.pause();
    }

    // 设置新的播放位置
    setCurrentIndex(index);
    const newAudio = audioRefs.current[index];
    if (newAudio) {
      newAudio.currentTime = 0;
      newAudio.play();
      setIsPlaying(true);
    }
  };

  // 获取段落的背景色
  const getSegmentBackground = (segmentIndex: number, isFirstSegment: boolean) => {
    if (segmentIndex === currentIndex) {
      // 当前播放的段落
      return isPlaying ? 'bg-gray-600' : 'bg-gray-500';
    } else if (segmentIndex < currentIndex) {
      // 已播放的段落
      return 'bg-gray-400';
    } else if (segmentIndex === 0 && isFirstSegment && !isPlaying && currentIndex === 0) {
      // 第一个段落，且未开始播放
      return 'bg-white';
    } else {
      // 未播放的段落
      return 'bg-white';
    }
  };

  // 处理自动播放
  useEffect(() => {
    if (autoPlayOnLoad && audioFiles.length > 0 && !isLoading) {
      const firstAudio = audioRefs.current[0];
      if (firstAudio) {
        firstAudio.play()
          .then(() => {
            setIsPlaying(true);
          })
          .catch(error => {
            console.error('Auto-play failed:', error);
          });
      }
    }
  }, [autoPlayOnLoad, audioFiles.length, isLoading]);

  // 处理音频切换
  useEffect(() => {
    if (audioFiles[currentIndex]) {
      const filename = audioFiles[currentIndex].filename.split('/').pop() || '';
      onAudioChange?.(filename);
    }
  }, [currentIndex, audioFiles, onAudioChange]);

  if (isLoading) {
    return <div className="text-center py-4">加载音频中...</div>;
  }

  const segments = calculateSegments();

  return (
    <div className="relative">
      {/* 进度条和控制按钮容器 */}
      <div className="flex items-center">
        {/* 播放按钮 */}
        <button
          onClick={togglePlay}
          className="h-8 w-8 flex items-center justify-center text-white hover:bg-white/10 rounded-full"
        >
          {isPlaying ? (
            <PauseIcon className="h-5 w-5" />
          ) : (
            <PlayIcon className="h-5 w-5" />
          )}
        </button>

        {/* 当前时间 */}
        <div className="text-sm text-white w-12 ml-2">
          {`${Math.floor(currentTime / 60)}:${Math.floor(currentTime % 60).toString().padStart(2, '0')}`}
        </div>

        {/* 进度条容器 */}
        <div className="flex-grow mx-2 relative">
          {/* 进度条 */}
          <div className="h-6 flex items-center bg-white rounded overflow-hidden relative">
            {/* 分隔线 */}
            {segments.map((segment, index) => (
              segment.isMajorBreak && (
                <div
                  key={`separator-${index}`}
                  className="absolute top-0 h-6 w-[2px] bg-gray-300 z-20"
                  style={{ left: `${segment.position}%` }}
                />
              )
            ))}

            {/* 音频段落 */}
            {segments.map((segment, index) => {
              const isFirstSegment = segment.section === 'stem' && 
                segments.filter(s => s.section === 'stem')[0] === segment;
              
              return (
                <div
                  key={`segment-${index}`}
                  className={`h-full transition-all cursor-pointer relative ${
                    getSegmentBackground(segment.audioIndex, isFirstSegment)
                  }`}
                  style={{ width: `${segment.width}%` }}
                  onClick={() => handleProgressClick(segment.audioIndex)}
                >
                  {!segment.isMajorBreak && index > 0 && (
                    <div 
                      className="absolute left-0 top-[15%] h-[70%] w-[1px] bg-gray-300 z-10"
                    />
                  )}
                  <audio
                    ref={el => {
                      if (el) {
                        audioRefs.current[segment.audioIndex] = el;
                      }
                    }}
                    src={`/api/audio?path=${encodeURIComponent(audioFiles[segment.audioIndex].url)}`}
                    onEnded={handleAudioEnd}
                    onTimeUpdate={updateCurrentTime}
                    onLoadedMetadata={(e) => {
                      // 不再需要实时计算时长，使用数据库中的值
                      console.log('[SerialAudioPlayer] 音频元数据已加载:', audioFiles[segment.audioIndex].filename);
                    }}
                  />
                </div>
              );
            })}
          </div>

          {/* 部分标签 */}
          <div className="absolute left-0 right-0 top-full mt-1">
            {Array.from(new Set(segments.map(s => s.section))).map((section) => {
              const sectionSegments = segments.filter(s => s.section === section);
              const totalWidth = sectionSegments.reduce((sum, seg) => sum + seg.width, 0);
              const startPosition = sectionSegments[0].position;

              return totalWidth > 0 && (
                <div
                  key={section}
                  className="absolute text-xs text-white"
                  style={{ 
                    width: `${totalWidth}%`,
                    textAlign: 'center',
                    left: `${startPosition}%`,
                    transform: 'translateX(-50%)',
                    marginLeft: `${totalWidth/2}%`
                  }}
                >
                  {getSectionLabel(section)}
                </div>
              );
            })}
          </div>
        </div>

        {/* 总时长 */}
        <div className="text-sm text-white w-12 text-right">
          {`${Math.floor(totalDuration / 60)}:${Math.floor(totalDuration % 60).toString().padStart(2, '0')}`}
        </div>
      </div>
    </div>
  );
} 