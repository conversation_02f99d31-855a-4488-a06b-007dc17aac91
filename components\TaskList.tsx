'use client'

import { useEffect, useState } from 'react'
import { useTaskStore } from '@/store/taskStore'
import { Task } from '@/types/task'

/**
 * TaskList组件 - 展示任务列表并处理轮询逻辑
 * - 自动轮询未完成任务的状态
 * - 首次20秒后开始轮询,之后每60秒轮询一次
 * - 最多轮询3次,超过则标记失败
 */
export default function TaskList() {
  const { tasks, updateTask } = useTaskStore()
  const [countdowns, setCountdowns] = useState<Record<string, number>>({})

  // 轮询任务状态
  useEffect(() => {
    const pollTask = async (task: Task) => {
      if (task.status === 'success' || task.status === 'failed') {
        return; // 如果任务已完成或失败，不再轮询
      }

      // 检查是否是直接生成动画的任务
      if (task.executeId.startsWith('direct_')) {
        // 直接生成动画的任务立即标记为成功
        updateTask(task.executeId, {
          status: 'success',
          result: '动画生成完成',
          lastPollTime: new Date().toISOString()
        });
        console.log('Direct animation task completed:', task.executeId);
        return;
      }

      try {
        const response = await fetch(`/api/status/${task.executeId}`);
        if (!response.ok) throw new Error('轮询失败');

        const data = await response.json();
        console.log('Poll response for task:', task.executeId, data);

        const updates: Partial<Task> = {
          pollCount: task.pollCount + 1,
          lastPollTime: new Date().toISOString()
        };

        // 如果有结果，立即标记为成功并停止轮询
        if (data.status === 'completed' && data.result) {
          updates.status = 'success';
          updates.result = data.result;
          console.log('Task completed:', task.executeId, updates);
        }
        // 如果超过轮询次数限制，标记为失败
        else if (task.pollCount >= 2) {
          updates.status = 'failed';
          updates.result = '轮询超时,请重试';
          console.log('Task failed (timeout):', task.executeId);
        }
        // 继续轮询
        else {
          updates.status = 'polling';
          console.log('Task still polling:', task.executeId);
        }

        updateTask(task.executeId, updates);
      } catch (error) {
        console.error('Poll error for task:', task.executeId, error);
        updateTask(task.executeId, {
          status: 'failed',
          result: '轮询出错,请重试'
        });
      }
    };

    // 设置轮询间隔
    const intervals = tasks
      .filter(task => task.status === 'waiting' || task.status === 'polling')
      .map(task => {
        // 对于直接生成动画的任务，立即执行（不延迟）
        const delay = task.executeId.startsWith('direct_') ? 0 : (task.pollCount === 0 ? 60000 : 60000);
        console.log('Setting poll interval for task:', {
          executeId: task.executeId,
          status: task.status,
          pollCount: task.pollCount,
          delay,
          isDirect: task.executeId.startsWith('direct_')
        });
        return setTimeout(() => pollTask(task), delay);
      });

    return () => {
      console.log('Cleaning up intervals');
      intervals.forEach(clearTimeout);
    };
  }, [tasks, updateTask]);

  // 倒计时更新
  useEffect(() => {
    const updateCountdowns = () => {
      const now = new Date().getTime()
      const newCountdowns: Record<string, number> = {}

      tasks.forEach(task => {
        if (task.status !== 'waiting' && task.status !== 'polling') return

        // 直接生成动画的任务不显示倒计时
        if (task.executeId.startsWith('direct_')) return

        const lastPollTime = task.lastPollTime ? new Date(task.lastPollTime).getTime() : task.createdAt.getTime()
        const delay = task.pollCount === 0 ? 60000 : 60000
        const nextPollTime = lastPollTime + delay
        const remaining = Math.max(0, Math.ceil((nextPollTime - now) / 1000))

        newCountdowns[task.executeId] = remaining
      })

      setCountdowns(newCountdowns)
    }

    const timer = setInterval(updateCountdowns, 1000)
    updateCountdowns()

    return () => clearInterval(timer)
  }, [tasks])

  // 空状态展示
  if (tasks.length === 0) {
    return (
      <div className="text-center text-gray-500 py-8">
        暂无任务,请创建新任务
      </div>
    )
  }

  // 任务列表展示
  return (
    <div className="space-y-4">
      {tasks.map(task => (
        <div 
          key={task.executeId} 
          className="border rounded-lg p-4 space-y-2"
        >
          <div className="flex justify-between items-center">
            <div className="space-y-1">
              <div className="text-sm text-gray-500">
                任务ID: {task.executeId}
              </div>
              {(task.status === 'waiting' || task.status === 'polling') && (
                <div className="text-sm text-blue-500">
                  下次轮询: {countdowns[task.executeId] || 0}秒
                </div>
              )}
            </div>
            <TaskStatus task={task} />
          </div>
          
          <div>
            <h3 className="font-medium mb-1">题目内容:</h3>
            <p className="text-gray-600 whitespace-pre-wrap">
              {task.content}
            </p>
          </div>

          {task.result && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-medium mb-2">原始返回结果:</h3>
              <div className="prose prose-sm max-w-none">
                <pre className="whitespace-pre-wrap overflow-auto">
                  {JSON.stringify(task.result, null, 2)}
                </pre>
              </div>
              <div className="text-sm text-gray-400 mt-2">
                更新时间: {new Date(task.lastPollTime || '').toLocaleString()}
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

/**
 * TaskStatus组件 - 展示任务状态
 * - 使用不同颜色区分不同状态
 * - 展示轮询次数
 */
function TaskStatus({ task }: { task: Task }) {
  const statusMap = {
    waiting: '等待轮询',
    polling: `轮询中 (${task.pollCount}/3)`,
    success: '调用成功',
    failed: '调用失败'
  }

  const colorMap = {
    waiting: 'bg-yellow-100 text-yellow-800',
    polling: 'bg-blue-100 text-blue-800',
    success: 'bg-green-100 text-green-800',
    failed: 'bg-red-100 text-red-800'
  }

  return (
    <span className={`px-2 py-1 rounded-full text-sm ${colorMap[task.status]}`}>
      {statusMap[task.status]}
    </span>
  )
} 