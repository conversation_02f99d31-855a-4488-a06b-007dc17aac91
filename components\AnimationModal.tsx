'use client';

import { useEffect, useState, useRef } from 'react';
import { Dialog } from '@headlessui/react';
import { ArrowsPointingOutIcon, VideoCameraIcon } from '@heroicons/react/24/outline';
import SerialAudioPlayer from './SerialAudioPlayer';
import QuestionContent from './QuestionContent';
import { VideoRecorder, type RecordingState } from '@/lib/videoRecorder';
import { checkRecordingSupport, getDisplayMessage } from '@/lib/videoRecordingSupport';

interface AnimationModalProps {
  isOpen: boolean;
  onClose: () => void;
  data: {
    new_response: string | null;
    stem_txt: string;
  };
}

interface AudioInfo {
  url: string;
  filename: string;
  section: 'stem' | 'analysis' | 'answer' | 'summary';
  sectionIndex: number;
  duration?: number;
  isLoaded: boolean;
  loadTime?: number;
  error?: string;
}

interface ParsedContent {
  stem: {
    url: string;
    yw?: string;
    zm?: string;  // 字幕
    mark?: Array<{text: string; type: 'circle' | 'wave'}>; // 标记（暂时忽略）
  }[];
  analysis: {
    url: string;
    zm?: string;  // 字幕
  }[];
  answer: {
    url: string;
    zm?: string;  // 字幕
  }[];
  summary: {
    url: string;
    zm?: string;  // 字幕
  }[];
}

export default function AnimationModal({ isOpen, onClose, data }: AnimationModalProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [audioFiles, setAudioFiles] = useState<AudioInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalLoadTime, setTotalLoadTime] = useState(0);
  const [showControls, setShowControls] = useState(false);
  const [allAudiosLoaded, setAllAudiosLoaded] = useState(false);
  const [highlightTexts, setHighlightTexts] = useState<string[]>([]);
  const [currentAudioFile, setCurrentAudioFile] = useState<string>('');
  const [audioDurationsLoaded, setAudioDurationsLoaded] = useState(false);
  const [subtitleTexts, setSubtitleTexts] = useState<string[]>([]);  // 字幕文本

  // 录制相关状态
  const [recordingState, setRecordingState] = useState<RecordingState>({
    status: 'idle',
    progress: 0,
    duration: 0
  });
  const [recordingSupported, setRecordingSupported] = useState(false);
  const [recordingError, setRecordingError] = useState<string | null>(null);

  // 录制器引用
  const videoRecorderRef = useRef<VideoRecorder | null>(null);
  const modalRef = useRef<HTMLDivElement>(null);

  // 处理点击事件
  const handlePanelClick = (e: React.MouseEvent) => {
    // 如果点击的是控制区域，不切换控制显示状态
    if ((e.target as HTMLElement).closest('.control-area')) {
      return;
    }
    setShowControls(!showControls);
  };

  // 处理音频切换的函数
  const handleAudioChange = (filename: string) => {
    setCurrentAudioFile(filename);
  };

  useEffect(() => {
    if (data.new_response) {
      try {
        const content = JSON.parse(data.new_response) as ParsedContent;
        
        // 提取所有 stem 部分的 yw 文本
        const ywTexts = content.stem
          .map(item => item.yw)
          .filter((text): text is string => !!text);

        setHighlightTexts(ywTexts);

        // 提取所有部分的字幕文本
        const allSubtitles: string[] = [];
        const sections: Array<keyof ParsedContent> = ['stem', 'analysis', 'answer', 'summary'];
        sections.forEach(section => {
          const sectionItems = content[section] || [];
          sectionItems.forEach(item => {
            if (item.zm) {
              allSubtitles.push(item.zm);
            }
          });
        });

        setSubtitleTexts(allSubtitles);
        
        // 按照sections顺序组织音频文件
        const orderedAudios: AudioInfo[] = [];
        sections.forEach((section, sectionIndex) => {
          const sectionAudios = content[section] || [];
          sectionAudios.forEach((item, index) => {
            if (item.url) {
              orderedAudios.push({
                url: item.url,
                filename: item.url.split('/').pop() || '',
                section,
                sectionIndex,
                isLoaded: false,
                duration: undefined // 将从数据库获取
              });
            }
          });
        });

        // 先设置基础音频文件信息
        setAudioFiles(orderedAudios);

        // 异步获取音频时长信息
        const loadAudioDurations = async () => {
          try {
            console.log('[AnimationModal] 开始获取音频时长信息');
            // 从URL中提取task_id（假设URL格式为 storage/{stem_id}/audio/{index}.mp3）
            const firstAudioUrl = orderedAudios[0]?.url;
            if (firstAudioUrl) {
              const pathParts = firstAudioUrl.split('/');
              const stemId = pathParts[1]; // storage/{stem_id}/audio/{index}.mp3

              console.log('[AnimationModal] 提取stemId:', stemId);

              // 调用API获取音频信息
              const response = await fetch(`/api/audio-durations?stemId=${stemId}`);
              if (response.ok) {
                const audioData = await response.json();
                console.log('[AnimationModal] 获取到音频数据:', audioData);

                // 更新音频时长信息
                setAudioFiles(prevFiles => {
                  const updatedFiles = prevFiles.map(audio => {
                    const audioRecord = audioData.find((record: any) =>
                      record.local_path === audio.url
                    );
                    if (audioRecord && audioRecord.duration) {
                      console.log(`[AnimationModal] 更新音频时长: ${audio.filename} -> ${audioRecord.duration}秒`);
                      return { ...audio, duration: audioRecord.duration };
                    }
                    return audio;
                  });

                  // 检查是否所有音频都有时长信息
                  const allHaveDuration = updatedFiles.every(audio => audio.duration);
                  if (allHaveDuration) {
                    console.log('[AnimationModal] 所有音频时长信息加载完成');
                    setAudioDurationsLoaded(true);
                  }

                  return updatedFiles;
                });
              } else {
                console.warn('[AnimationModal] API响应失败:', response.status);
                // API失败时，标记为已加载但没有时长信息
                setAudioDurationsLoaded(true);
              }
            } else {
              // 没有音频文件时，直接标记为已加载
              setAudioDurationsLoaded(true);
            }
          } catch (error) {
            console.error('[AnimationModal] 获取音频时长失败:', error);
            // 错误时也标记为已加载，避免无限等待
            setAudioDurationsLoaded(true);
          }
        };

        // 异步加载音频时长
        loadAudioDurations();
        
        // 按顺序加载音频文件
        const loadAudios = async () => {
          setLoading(true);
          setAllAudiosLoaded(false);
          const startTime = Date.now();
          
          for (let i = 0; i < orderedAudios.length; i++) {
            const audioInfo = orderedAudios[i];
            const loadStartTime = Date.now();
            
            try {
              const response = await fetch(`/api/audio?path=${encodeURIComponent(audioInfo.url)}`);
              if (!response.ok) throw new Error('Failed to load audio');
              
              const loadEndTime = Date.now();
              
              setAudioFiles(prev => {
                const newFiles = [...prev];
                newFiles[i] = { 
                  ...newFiles[i], 
                  isLoaded: true,
                  loadTime: loadEndTime - loadStartTime
                };
                return newFiles;
              });
        } catch (error) {
              setAudioFiles(prev => {
                const newFiles = [...prev];
                newFiles[i] = { 
                  ...newFiles[i], 
                  isLoaded: false, 
                  error: error instanceof Error ? error.message : '加载失败'
                };
                return newFiles;
              });
            }
          }
          
          setTotalLoadTime(Date.now() - startTime);
          setLoading(false);
          setAllAudiosLoaded(true);
        };

        loadAudios();
      } catch (error) {
        console.error('Error parsing response:', error);
        setLoading(false);
      }
    }
  }, [data.new_response]);

  return (
      <Dialog 
      open={isOpen}
      onClose={onClose}
        className="relative z-50" 
      >
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
        
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel
          className={`relative bg-[#F0F8FF] rounded-lg shadow-xl transition-all
            ${isFullscreen ? 'w-full h-full' : 'w-[896px] h-[414px]'}`}
          onClick={handlePanelClick}
        >
          {/* 加载动画层 - z-30 */}
          {loading && (
            <div className="absolute inset-0 flex items-center justify-center bg-[#F0F8FF] z-30">
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
                <p className="mt-4 text-gray-600">加载音频中...</p>
              </div>
                      </div>
          )}

          {/* 内容展示层 - z-10 */}
          <div className="absolute inset-0 z-10 p-6">
            {/* 只有在音频时长信息完全加载后才渲染QuestionContent */}
            {audioDurationsLoaded ? (
              <QuestionContent
                data={{
                  ...data,
                  new_response: data.new_response,
                  currentAudioFile,
                  audioFiles: audioFiles.map(audio => ({
                    url: audio.url,
                    filename: audio.filename,
                    duration: audio.duration
                  }))
                }}
                isAnimation={true}
              />
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-white text-lg">正在加载音频时长信息...</div>
              </div>
            )}
                  </div>

          {/* 控制蒙层 - z-20 */}
          <div 
            className={`absolute inset-0 transition-opacity duration-200 z-20 ${
              showControls ? 'opacity-100' : 'opacity-0 pointer-events-none'
            }`}
          >
            {/* 顶部控制区渐变背景 */}
            <div className="absolute top-0 left-0 right-0 h-20 bg-gradient-to-b from-black/40 to-transparent" />
            
            {/* 底部控制区渐变背景 */}
            <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/40 to-transparent" />
            
            {/* 顶部控制栏 */}
            <div className="absolute top-0 left-0 right-0 flex justify-end items-center p-4 control-area">
              <div className="flex gap-2">
                  <button
                  onClick={() => setIsFullscreen(!isFullscreen)}
                  className="p-2 hover:bg-white/10 rounded-full text-white"
                >
                  <ArrowsPointingOutIcon className="w-5 h-5" />
                  </button>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-white/10 rounded-full text-white"
                >
                  ×
                </button>
                      </div>
                    </div>

            {/* 底部播放控制区 */}
            <div className="absolute bottom-8 left-0 right-0 px-6 control-area">
              <SerialAudioPlayer
                audioFiles={audioFiles}
                isLoading={loading}
                autoPlayOnLoad={allAudiosLoaded}
                onAudioChange={handleAudioChange}
              />
                </div>
              </div>
            </Dialog.Panel>
        </div>
      </Dialog>
  );
} 