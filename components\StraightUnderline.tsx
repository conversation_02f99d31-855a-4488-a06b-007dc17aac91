'use client';

import { useEffect, useRef } from 'react';

interface StraightUnderlineProps {
  width: number;      
  duration: number;   
  color?: string;     
  delay?: number;     
}

export default function StraightUnderline({
  width,
  duration = 1,
  color = '#9CA3AF',  // 使用灰色，区别于波浪线的红色
  delay = 1
}: StraightUnderlineProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  console.log('📏 [直线] 创建直线组件:', { width, duration, delay });
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 正确处理高分辨率屏幕，确保像素对齐
    const scale = window.devicePixelRatio || 1;
    const canvasHeight = 4; // 直线高度固定为4px

    canvas.width = Math.round(width * scale);
    canvas.height = Math.round(canvasHeight * scale);
    canvas.style.width = `${width}px`;
    canvas.style.height = `${canvasHeight}px`;
    ctx.scale(scale, scale);

    // 确保绘制位置精确对齐
    ctx.translate(0.5, 0.5);

    const startTime = performance.now();
    const animationDuration = duration * 1000;
    
    const animate = (currentTime: number) => {
      const elapsed = Math.max(currentTime - startTime - (delay * 1000), 0);
      const progress = duration === 0 ? 1 : Math.min(elapsed / animationDuration, 1);

      ctx.clearRect(0, 0, canvas.width, canvas.height);

      ctx.beginPath();
      ctx.strokeStyle = color;
      ctx.lineWidth = scale * 0.8; // 稍微粗一点的线条
      ctx.lineCap = 'round';

      const currentWidth = width * progress;
      const y = canvasHeight / 2; // 在画布中央绘制直线

      // 绘制直线
      ctx.moveTo(0, y);
      ctx.lineTo(currentWidth, y);

      ctx.stroke();

      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate);
      }
    };
    
    if (delay === 0 && duration === 0) {
      // 立即显示完整状态（已完成标记）
      console.log('📏 [直线] 立即显示完整状态（已完成标记）');
      animationRef.current = requestAnimationFrame(animate);
    } else if (delay === 0) {
      // 立即开始动画
      console.log('📏 [直线] 立即开始动画');
      animationRef.current = requestAnimationFrame(animate);
    } else {
      console.log(`📏 [直线] 延迟 ${delay} 秒后开始动画`);
      setTimeout(() => {
        console.log('📏 [直线] 延迟时间到，开始动画');
        animationRef.current = requestAnimationFrame(animate);
      }, delay * 1000);
    }
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [width, duration, color, delay]);

  return (
    <canvas
      ref={canvasRef}
      className="absolute left-0 pointer-events-none"
      style={{
        width: `${width}px`,
        height: '4px'
      }}
    />
  );
}
