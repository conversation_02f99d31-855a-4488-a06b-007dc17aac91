import Database from 'better-sqlite3';
import path from 'path';

const db = new Database(path.join(process.cwd(), 'local.db'));

try {
  // 开启外键支持
  db.pragma('foreign_keys = ON');
  
  console.log('\n=== 测试外键约束 ===\n');

  // 1. 测试插入无效的stem_id到tasks表
  console.log('测试1: 尝试插入指向不存在stem_id的任务');
  try {
    db.prepare(`
      INSERT INTO tasks (task_id, stem_id, status) 
      VALUES ('test_task_1', 999999, 'pending')
    `).run();
    console.log('❌ 失败：成功插入了无效的stem_id');
  } catch (error) {
    console.log('✅ 成功：外键约束阻止了无效stem_id的插入');
  }

  // 2. 测试插入无效的task_id到audio表
  console.log('\n测试2: 尝试插入指向不存在task_id的音频');
  try {
    db.prepare(`
      INSERT INTO audio (audio_name, task_id, url, local_path, status) 
      VALUES ('test.mp3', 'invalid_task_id', 'http://test.com', '/local/path', 'pending')
    `).run();
    console.log('❌ 失败：成功插入了无效的task_id');
  } catch (error) {
    console.log('✅ 成功：外键约束阻止了无效task_id的插入');
  }

  // 3. 测试正常的插入流程
  console.log('\n测试3: 测试正常的插入流程');
  try {
    // 3.1 插入stem
    const stemResult = db.prepare(`
      INSERT INTO stems (content) 
      VALUES ('测试题目内容')
    `).run();
    const stemId = stemResult.lastInsertRowid;
    console.log(`✅ 成功插入stem，ID: ${stemId}`);

    // 3.2 插入task
    const taskId = 'test_task_' + Date.now();
    db.prepare(`
      INSERT INTO tasks (task_id, stem_id, status) 
      VALUES (?, ?, 'pending')
    `).run(taskId, stemId);
    console.log(`✅ 成功插入task，ID: ${taskId}`);

    // 3.3 插入audio
    db.prepare(`
      INSERT INTO audio (audio_name, task_id, url, local_path, status) 
      VALUES ('test.mp3', ?, 'http://test.com', '/local/path', 'pending')
    `).run(taskId);
    console.log('✅ 成功插入audio记录');

    // 3.4 测试删除约束
    console.log('\n测试4: 测试删除约束');
    try {
      db.prepare('DELETE FROM stems WHERE stem_id = ?').run(stemId);
      console.log('❌ 失败：成功删除了有关联task的stem');
    } catch (error) {
      console.log('✅ 成功：外键约束阻止了删除有关联task的stem');
    }

    // 清理测试数据
    console.log('\n=== 清理测试数据 ===');
    db.prepare('DELETE FROM audio WHERE task_id = ?').run(taskId);
    db.prepare('DELETE FROM tasks WHERE task_id = ?').run(taskId);
    db.prepare('DELETE FROM stems WHERE stem_id = ?').run(stemId);
    console.log('✅ 成功清理测试数据');

  } catch (error) {
    console.error('\n❌ 测试过程中出现错误:', error);
    throw error;
  }

} catch (error) {
  console.error('\n❌ 测试失败:', error);
} finally {
  db.close();
} 