import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

// 数据库配置
const DB_PATH = path.join(process.cwd(), 'local.db');
const STORAGE_PATH = path.join(process.cwd(), 'storage');

/**
 * 初始化存储目录
 */
function initializeStorage() {
  if (!fs.existsSync(STORAGE_PATH)) {
    fs.mkdirSync(STORAGE_PATH);
    console.log('✅ Created storage directory');
  }
}

/**
 * 初始化数据库表
 */
function initializeDatabase() {
  const db = new Database(DB_PATH, { verbose: console.log });

  try {
    // 创建题目表
    db.exec(`
      CREATE TABLE IF NOT EXISTS stems (
        stem_id INTEGER PRIMARY KEY AUTOINCREMENT,
        content TEXT NOT NULL,
        images TEXT
      );
    `);

    // 创建任务表
    db.exec(`
      CREATE TABLE IF NOT EXISTS tasks (
        task_id TEXT PRIMARY KEY,
        stem_id INTEGER NOT NULL,
        status TEXT NOT NULL,
        raw_response TEXT,
        new_response TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (stem_id) REFERENCES stems(stem_id)
      );
    `);

    // 创建视频表
    db.exec(`
      CREATE TABLE IF NOT EXISTS videos (
        video_id INTEGER PRIMARY KEY AUTOINCREMENT,
        task_id TEXT NOT NULL,
        scene_data TEXT NOT NULL,
        output_path TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (task_id) REFERENCES tasks(task_id)
      );
    `);

    // 创建音频表
    db.exec(`
      CREATE TABLE IF NOT EXISTS audio (
        audio_id INTEGER PRIMARY KEY AUTOINCREMENT,
        audio_name TEXT NOT NULL,
        task_id TEXT NOT NULL,
        url TEXT NOT NULL,
        local_path TEXT NOT NULL,
        status TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (task_id) REFERENCES tasks(task_id)
      );
    `);

    console.log('✅ Database tables created successfully');
  } catch (error) {
    console.error('❌ Failed to initialize database:', error);
    process.exit(1);
  } finally {
    db.close();
  }
}

/**
 * 主初始化函数
 */
function initialize() {
  console.log('🚀 Starting initialization...');
  
  try {
    initializeStorage();
    initializeDatabase();
    console.log('✅ Initialization completed successfully');
  } catch (error) {
    console.error('❌ Initialization failed:', error);
    process.exit(1);
  }
}

// 运行初始化
initialize(); 