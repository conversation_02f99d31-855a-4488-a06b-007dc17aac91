/**
 * 视频录制核心类
 * 实现Canvas合成、MediaRecorder录制、音视频同步等功能
 */

import { checkRecordingSupport, getRecommendedRecordingConfig, type RecordingSupportInfo } from './videoRecordingSupport';
import { videoConverter, type ConversionProgress } from './videoConverter';

export interface RecordingConfig {
  width: number;
  height: number;
  frameRate: number;
  videoBitsPerSecond: number;
  audioBitsPerSecond: number;
  mimeType: string;
}

export interface RecordingState {
  status: 'idle' | 'preparing' | 'countdown' | 'recording' | 'stopping' | 'processing' | 'converting' | 'completed' | 'error';
  progress: number;
  duration: number;
  error?: string;
  blob?: Blob;
  downloadUrl?: string;
  convertedBlob?: Blob;
  convertedDownloadUrl?: string;
  conversionProgress?: ConversionProgress;
}

export type RecordingEventType = 'stateChange' | 'progress' | 'error' | 'completed';

export interface RecordingEventData {
  state: RecordingState;
  type: RecordingEventType;
}

export class VideoRecorder {
  private config: RecordingConfig;
  private support: RecordingSupportInfo;
  private state: RecordingState;
  private eventListeners: Map<RecordingEventType, Set<(data: RecordingEventData) => void>>;
  
  // 录制相关
  private mediaRecorder: MediaRecorder | null = null;
  private recordedChunks: Blob[] = [];
  private startTime: number = 0;
  private animationFrameId: number | null = null;
  
  // Canvas相关
  private mainCanvas: HTMLCanvasElement | null = null;
  private mainContext: CanvasRenderingContext2D | null = null;
  private stream: MediaStream | null = null;

  // 录制目标相关
  private targetElement: HTMLElement | null = null;
  private canvasElements: HTMLCanvasElement[] = [];
  private audioElements: HTMLAudioElement[] = [];

  // 音频相关
  private audioContext: AudioContext | null = null;
  private audioDestination: MediaStreamAudioDestinationNode | null = null;
  private audioSources: MediaStreamAudioSourceNode[] = [];

  constructor(config?: Partial<RecordingConfig>) {
    // 检测浏览器支持
    this.support = checkRecordingSupport();
    
    // 获取推荐配置
    const recommendedConfig = getRecommendedRecordingConfig(this.support);
    
    // 合并配置
    this.config = {
      width: 1920,
      height: 1080,
      frameRate: 30,
      videoBitsPerSecond: recommendedConfig.videoBitsPerSecond || 8000000,
      audioBitsPerSecond: recommendedConfig.audioBitsPerSecond || 128000,
      mimeType: recommendedConfig.mimeType,
      ...config
    };

    // 初始化状态
    this.state = {
      status: 'idle',
      progress: 0,
      duration: 0
    };

    // 初始化事件监听器
    this.eventListeners = new Map();
    
    console.log('🎥 VideoRecorder initialized:', {
      config: this.config,
      support: this.support.supportLevel
    });
  }

  /**
   * 检查是否支持录制
   */
  public isSupported(): boolean {
    return this.support.isFullySupported || this.support.supportLevel === 'good';
  }

  /**
   * 获取支持信息
   */
  public getSupportInfo(): RecordingSupportInfo {
    return this.support;
  }

  /**
   * 添加事件监听器
   */
  public addEventListener(type: RecordingEventType, callback: (data: RecordingEventData) => void): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, new Set());
    }
    this.eventListeners.get(type)!.add(callback);
  }

  /**
   * 移除事件监听器
   */
  public removeEventListener(type: RecordingEventType, callback: (data: RecordingEventData) => void): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  /**
   * 触发事件
   */
  private emitEvent(type: RecordingEventType): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      const eventData: RecordingEventData = {
        state: { ...this.state },
        type
      };
      listeners.forEach(callback => callback(eventData));
    }
  }

  /**
   * 更新状态
   */
  private updateState(updates: Partial<RecordingState>): void {
    this.state = { ...this.state, ...updates };
    this.emitEvent('stateChange');
  }

  /**
   * 准备录制环境
   */
  public async prepare(targetElement: HTMLElement): Promise<void> {
    if (!this.isSupported()) {
      throw new Error('浏览器不支持视频录制功能');
    }

    this.updateState({ status: 'preparing' });

    try {
      // 创建主Canvas
      await this.createMainCanvas();
      
      // 设置音频上下文
      await this.setupAudioContext();
      
      // 准备录制目标
      await this.prepareRecordingTarget(targetElement);
      
      console.log('🎥 录制环境准备完成');
      this.updateState({ status: 'idle' });
      
    } catch (error) {
      console.error('🎥 录制环境准备失败:', error);
      this.updateState({ 
        status: 'error', 
        error: error instanceof Error ? error.message : '准备录制环境失败' 
      });
      throw error;
    }
  }

  /**
   * 创建主Canvas
   */
  private async createMainCanvas(): Promise<void> {
    this.mainCanvas = document.createElement('canvas');
    this.mainCanvas.width = this.config.width;
    this.mainCanvas.height = this.config.height;
    
    this.mainContext = this.mainCanvas.getContext('2d');
    if (!this.mainContext) {
      throw new Error('无法创建Canvas 2D上下文');
    }

    // 设置高质量渲染
    this.mainContext.imageSmoothingEnabled = true;
    this.mainContext.imageSmoothingQuality = 'high';
    
    console.log('🎥 主Canvas创建完成:', {
      width: this.config.width,
      height: this.config.height
    });
  }

  /**
   * 设置音频上下文
   */
  private async setupAudioContext(): Promise<void> {
    if (!this.support.webAudioAPI) {
      console.warn('🎥 Web Audio API不支持，将跳过音频录制');
      return;
    }

    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      this.audioDestination = this.audioContext.createMediaStreamDestination();
      
      console.log('🎥 音频上下文设置完成');
    } catch (error) {
      console.warn('🎥 音频上下文设置失败:', error);
    }
  }

  /**
   * 准备录制目标
   */
  private async prepareRecordingTarget(targetElement: HTMLElement): Promise<void> {
    // 存储目标元素引用
    this.targetElement = targetElement;

    // 收集所有需要录制的Canvas元素
    this.collectCanvasElements(targetElement);

    // 收集音频元素
    this.collectAudioElements(targetElement);

    console.log('🎥 准备录制目标完成:', {
      canvasCount: this.canvasElements.length,
      audioCount: this.audioElements.length
    });
  }

  /**
   * 开始录制（带倒计时）
   */
  public async startRecording(countdownSeconds: number = 3): Promise<void> {
    if (this.state.status !== 'idle') {
      throw new Error('录制器未准备就绪');
    }

    try {
      // 倒计时
      if (countdownSeconds > 0) {
        await this.countdown(countdownSeconds);
      }

      // 开始实际录制
      await this.beginRecording();
      
    } catch (error) {
      console.error('🎥 开始录制失败:', error);
      this.updateState({ 
        status: 'error', 
        error: error instanceof Error ? error.message : '开始录制失败' 
      });
      throw error;
    }
  }

  /**
   * 倒计时
   */
  private async countdown(seconds: number): Promise<void> {
    this.updateState({ status: 'countdown' });
    
    for (let i = seconds; i > 0; i--) {
      console.log(`🎥 倒计时: ${i}`);
      this.updateState({ progress: (seconds - i) / seconds * 100 });
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  /**
   * 开始实际录制
   */
  private async beginRecording(): Promise<void> {
    if (!this.mainCanvas) {
      throw new Error('主Canvas未初始化');
    }

    // 创建视频流
    this.stream = this.mainCanvas.captureStream(this.config.frameRate);
    
    // 添加音频轨道
    if (this.audioDestination) {
      const audioTracks = this.audioDestination.stream.getAudioTracks();
      audioTracks.forEach(track => {
        this.stream!.addTrack(track);
      });
    }

    // 创建MediaRecorder
    this.mediaRecorder = new MediaRecorder(this.stream, {
      mimeType: this.config.mimeType,
      videoBitsPerSecond: this.config.videoBitsPerSecond,
      audioBitsPerSecond: this.config.audioBitsPerSecond
    });

    // 设置事件监听器
    this.setupMediaRecorderEvents();

    // 开始录制
    this.recordedChunks = [];
    this.startTime = performance.now();
    this.mediaRecorder.start(100); // 每100ms收集一次数据
    
    this.updateState({ status: 'recording', duration: 0 });
    
    // 开始渲染循环
    this.startRenderLoop();
    
    console.log('🎥 开始录制');
  }

  /**
   * 设置MediaRecorder事件
   */
  private setupMediaRecorderEvents(): void {
    if (!this.mediaRecorder) return;

    this.mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        this.recordedChunks.push(event.data);
      }
    };

    this.mediaRecorder.onstop = () => {
      this.handleRecordingComplete();
    };

    this.mediaRecorder.onerror = (event) => {
      console.error('🎥 MediaRecorder错误:', event);
      this.updateState({ 
        status: 'error', 
        error: 'MediaRecorder录制错误' 
      });
    };
  }

  /**
   * 开始渲染循环
   */
  private startRenderLoop(): void {
    const render = () => {
      if (this.state.status !== 'recording') {
        return;
      }

      // 更新录制时长
      const currentTime = performance.now();
      const duration = (currentTime - this.startTime) / 1000;
      this.updateState({ duration });

      // 渲染帧（具体实现将在下个文件中完成）
      this.renderFrame();

      // 继续下一帧
      this.animationFrameId = requestAnimationFrame(render);
    };

    this.animationFrameId = requestAnimationFrame(render);
  }

  /**
   * 渲染单帧
   */
  private renderFrame(): void {
    if (!this.mainContext || !this.targetElement) return;

    // 清空画布
    this.mainContext.clearRect(0, 0, this.config.width, this.config.height);

    // 设置背景色
    this.mainContext.fillStyle = '#F0F8FF'; // 与AnimationModal背景色一致
    this.mainContext.fillRect(0, 0, this.config.width, this.config.height);

    // 渲染DOM内容到Canvas
    this.renderDOMToCanvas();

    // 渲染所有动画Canvas
    this.renderAnimationCanvases();
  }

  /**
   * 收集Canvas元素
   */
  private collectCanvasElements(element: HTMLElement): void {
    this.canvasElements = [];

    // 查找所有Canvas元素
    const canvases = element.querySelectorAll('canvas');
    canvases.forEach(canvas => {
      this.canvasElements.push(canvas);
    });
  }

  /**
   * 收集音频元素
   */
  private collectAudioElements(element: HTMLElement): void {
    this.audioElements = [];

    // 查找所有音频元素
    const audios = element.querySelectorAll('audio');
    audios.forEach(audio => {
      this.audioElements.push(audio);

      // 连接到音频上下文
      if (this.audioContext && this.audioDestination) {
        try {
          const source = this.audioContext.createMediaElementSource(audio);
          source.connect(this.audioDestination);
          this.audioSources.push(source);
        } catch (error) {
          console.warn('🎥 连接音频元素失败:', error);
        }
      }
    });
  }

  /**
   * 渲染DOM内容到Canvas
   */
  private renderDOMToCanvas(): void {
    if (!this.mainContext || !this.targetElement) return;

    // 获取目标元素的位置和尺寸
    const rect = this.targetElement.getBoundingClientRect();
    const scaleX = this.config.width / rect.width;
    const scaleY = this.config.height / rect.height;

    // 保存当前变换状态
    this.mainContext.save();

    // 应用缩放
    this.mainContext.scale(scaleX, scaleY);

    // 使用html2canvas的简化版本渲染DOM
    this.renderElementToCanvas(this.targetElement, 0, 0);

    // 恢复变换状态
    this.mainContext.restore();
  }

  /**
   * 渲染单个元素到Canvas（简化版html2canvas）
   */
  private renderElementToCanvas(element: HTMLElement, x: number, y: number): void {
    if (!this.mainContext) return;

    const style = window.getComputedStyle(element);
    const rect = element.getBoundingClientRect();

    // 跳过不可见元素
    if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
      return;
    }

    // 渲染背景色
    if (style.backgroundColor && style.backgroundColor !== 'rgba(0, 0, 0, 0)') {
      this.mainContext.fillStyle = style.backgroundColor;
      this.mainContext.fillRect(x, y, rect.width, rect.height);
    }

    // 渲染文本内容
    if (element.textContent && element.children.length === 0) {
      this.renderTextToCanvas(element, x, y);
    }

    // 递归渲染子元素
    Array.from(element.children).forEach(child => {
      if (child instanceof HTMLElement) {
        const childRect = child.getBoundingClientRect();
        const parentRect = element.getBoundingClientRect();
        const childX = x + (childRect.left - parentRect.left);
        const childY = y + (childRect.top - parentRect.top);
        this.renderElementToCanvas(child, childX, childY);
      }
    });
  }

  /**
   * 渲染文本到Canvas
   */
  private renderTextToCanvas(element: HTMLElement, x: number, y: number): void {
    if (!this.mainContext || !element.textContent) return;

    const style = window.getComputedStyle(element);
    const rect = element.getBoundingClientRect();

    // 设置字体样式
    this.mainContext.font = `${style.fontSize} ${style.fontFamily}`;
    this.mainContext.fillStyle = style.color;
    this.mainContext.textAlign = 'left';
    this.mainContext.textBaseline = 'top';

    // 渲染文本
    const text = element.textContent.trim();
    if (text) {
      this.mainContext.fillText(text, x, y);
    }
  }

  /**
   * 渲染动画Canvas
   */
  private renderAnimationCanvases(): void {
    if (!this.mainContext) return;

    this.canvasElements.forEach(canvas => {
      if (canvas.width > 0 && canvas.height > 0) {
        // 获取Canvas在目标元素中的位置
        const canvasRect = canvas.getBoundingClientRect();
        const targetRect = this.targetElement!.getBoundingClientRect();

        // 计算相对位置
        const relativeX = canvasRect.left - targetRect.left;
        const relativeY = canvasRect.top - targetRect.top;

        // 计算缩放后的位置
        const scaleX = this.config.width / targetRect.width;
        const scaleY = this.config.height / targetRect.height;
        const scaledX = relativeX * scaleX;
        const scaledY = relativeY * scaleY;
        const scaledWidth = canvasRect.width * scaleX;
        const scaledHeight = canvasRect.height * scaleY;

        // 绘制Canvas内容
        this.mainContext.drawImage(
          canvas,
          scaledX,
          scaledY,
          scaledWidth,
          scaledHeight
        );
      }
    });
  }

  /**
   * 停止录制
   */
  public async stopRecording(): Promise<void> {
    if (this.state.status !== 'recording') {
      throw new Error('当前没有进行录制');
    }

    this.updateState({ status: 'stopping' });

    // 停止渲染循环
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }

    // 停止MediaRecorder
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.stop();
    }

    console.log('🎥 停止录制');
  }

  /**
   * 处理录制完成
   */
  private async handleRecordingComplete(): Promise<void> {
    this.updateState({ status: 'processing' });

    try {
      // 合并录制数据
      const blob = new Blob(this.recordedChunks, {
        type: this.config.mimeType
      });

      // 创建下载URL
      const downloadUrl = URL.createObjectURL(blob);

      this.updateState({
        blob,
        downloadUrl
      });

      console.log('🎥 录制完成:', {
        size: blob.size,
        type: blob.type,
        duration: this.state.duration
      });

      // 自动转换为MP4格式
      await this.convertToMP4(blob);

    } catch (error) {
      console.error('🎥 处理录制数据失败:', error);
      this.updateState({
        status: 'error',
        error: '处理录制数据失败'
      });
    }
  }

  /**
   * 转换视频为MP4格式
   */
  private async convertToMP4(webmBlob: Blob): Promise<void> {
    try {
      this.updateState({ status: 'converting' });

      // 检查转换支持
      const support = await videoConverter.checkConversionSupport();
      if (!support.supported) {
        console.warn('🎥 服务器不支持视频转换，跳过转换步骤');
        this.updateState({
          status: 'completed'
        });
        this.emitEvent('completed');
        return;
      }

      console.log('🎥 开始转换视频为MP4格式');

      // 执行转换
      const convertedBlob = await videoConverter.convertToMP4(webmBlob, (progress) => {
        this.updateState({
          conversionProgress: progress,
          progress: progress.progress
        });
      });

      // 创建转换后的下载URL
      const convertedDownloadUrl = URL.createObjectURL(convertedBlob);

      this.updateState({
        status: 'completed',
        convertedBlob,
        convertedDownloadUrl,
        progress: 100
      });

      this.emitEvent('completed');

      console.log('🎥 视频转换完成:', {
        originalSize: webmBlob.size,
        convertedSize: convertedBlob.size
      });

    } catch (error) {
      console.error('🎥 视频转换失败:', error);

      // 转换失败但录制成功，仍然标记为完成
      this.updateState({
        status: 'completed',
        error: `视频转换失败: ${error instanceof Error ? error.message : '未知错误'}，但WebM格式的视频仍可下载`
      });

      this.emitEvent('completed');
    }
  }

  /**
   * 获取当前状态
   */
  public getState(): RecordingState {
    return { ...this.state };
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    // 停止录制
    if (this.state.status === 'recording') {
      this.stopRecording().catch(console.error);
    }

    // 清理动画帧
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
    }

    // 清理媒体流
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
    }

    // 清理音频上下文
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close().catch(console.error);
    }

    // 清理下载URL
    if (this.state.downloadUrl) {
      URL.revokeObjectURL(this.state.downloadUrl);
    }

    // 清理事件监听器
    this.eventListeners.clear();

    console.log('🎥 VideoRecorder资源已清理');
  }
}
