# 视频录制功能文档

## 概述

本项目集成了完整的视频录制功能，可以将动画演示录制为高质量的MP4视频文件。该功能包括：

- 🎥 浏览器原生录制（MediaRecorder API）
- 🎨 Canvas动画捕获
- 🔊 音频同步录制
- 🔄 自动格式转换（WebM → MP4）
- 📱 跨浏览器兼容性
- ⚡ 高性能录制和处理

## 功能特性

### 录制功能
- **分辨率**: 1920x1080 (1080p)
- **帧率**: 30fps
- **音频**: 128kbps AAC编码
- **视频**: H.264编码，CRF 23质量
- **格式**: 原生WebM，自动转换为MP4

### 浏览器支持
- ✅ Chrome 51+ (推荐)
- ✅ Firefox 43+
- ✅ Safari 14.1+
- ✅ Edge 79+

## 安装和配置

### 1. 安装依赖

运行安装脚本：
```bash
node scripts/install-video-deps.js
```

或手动安装：
```bash
# 生产依赖
npm install uuid @types/uuid

# 开发依赖（用于服务器端视频转换）
npm install --save-dev fluent-ffmpeg ffmpeg-static @types/fluent-ffmpeg
```

### 2. 环境配置

确保项目根目录有 `temp/video` 目录的写入权限，用于临时文件存储。

## 使用方法

### 基础使用

在任何组件中集成录制功能：

```typescript
import { VideoRecorder, type RecordingState } from '@/lib/videoRecorder';
import { checkRecordingSupport } from '@/lib/videoRecordingSupport';

function MyComponent() {
  const [recordingState, setRecordingState] = useState<RecordingState>({
    status: 'idle',
    progress: 0,
    duration: 0
  });
  
  const videoRecorderRef = useRef<VideoRecorder | null>(null);
  const targetRef = useRef<HTMLDivElement>(null);

  // 检查支持
  const isSupported = checkRecordingSupport().isFullySupported;

  // 开始录制
  const startRecording = async () => {
    if (!targetRef.current) return;

    const recorder = new VideoRecorder();
    videoRecorderRef.current = recorder;

    // 监听状态变化
    recorder.addEventListener('stateChange', (data) => {
      setRecordingState(data.state);
    });

    // 准备并开始录制
    await recorder.prepare(targetRef.current);
    await recorder.startRecording(3); // 3秒倒计时
  };

  return (
    <div>
      <div ref={targetRef}>
        {/* 要录制的内容 */}
      </div>
      
      {isSupported && (
        <button onClick={startRecording}>
          开始录制
        </button>
      )}
    </div>
  );
}
```

### AnimationModal集成

录制功能已完全集成到 `AnimationModal` 组件中：

1. **录制按钮**: 在模态框顶部左侧
2. **状态指示**: 实时显示录制状态和进度
3. **自动转换**: 录制完成后自动转换为MP4格式
4. **下载选项**: 支持MP4和WebM格式下载

## API参考

### VideoRecorder类

#### 构造函数
```typescript
new VideoRecorder(config?: Partial<RecordingConfig>)
```

#### 主要方法
- `isSupported()`: 检查是否支持录制
- `prepare(targetElement)`: 准备录制环境
- `startRecording(countdown?)`: 开始录制（可选倒计时）
- `stopRecording()`: 停止录制
- `cleanup()`: 清理资源

#### 事件监听
- `stateChange`: 录制状态变化
- `progress`: 录制进度更新
- `error`: 录制错误
- `completed`: 录制完成

### 录制状态

```typescript
interface RecordingState {
  status: 'idle' | 'preparing' | 'countdown' | 'recording' | 
          'stopping' | 'processing' | 'converting' | 'completed' | 'error';
  progress: number;           // 0-100
  duration: number;           // 录制时长（秒）
  error?: string;             // 错误信息
  blob?: Blob;                // WebM格式视频
  downloadUrl?: string;       // WebM下载链接
  convertedBlob?: Blob;       // MP4格式视频
  convertedDownloadUrl?: string; // MP4下载链接
}
```

## 测试和调试

### 测试页面

访问 `/test-recording` 页面进行功能测试：

1. **支持检测**: 检查浏览器和服务器支持情况
2. **录制测试**: 测试基本录制功能
3. **实时日志**: 查看详细的执行日志
4. **下载测试**: 验证视频文件生成和下载

### 常见问题排查

#### 1. 录制不工作
- 检查浏览器支持：使用 `checkRecordingSupport()`
- 确认HTTPS环境（MediaRecorder需要安全上下文）
- 检查控制台错误信息

#### 2. 视频转换失败
- 确认FFmpeg依赖已安装
- 检查服务器磁盘空间
- 查看服务器日志中的FFmpeg错误

#### 3. 音频不同步
- 确认Web Audio API支持
- 检查音频元素是否正确连接
- 验证音频播放时机

## 性能优化

### 录制性能
- 使用合适的帧率（推荐30fps）
- 优化Canvas渲染性能
- 避免在录制期间进行重型计算

### 服务器性能
- 考虑使用外部FFmpeg服务
- 实现视频处理队列
- 定期清理临时文件

### 内存管理
- 及时调用 `cleanup()` 方法
- 避免同时进行多个录制
- 监控Blob对象的内存使用

## 部署注意事项

### 生产环境
1. **HTTPS必需**: MediaRecorder API需要安全上下文
2. **FFmpeg配置**: 确保服务器有FFmpeg支持
3. **文件权限**: 临时目录需要读写权限
4. **资源限制**: 设置合理的文件大小和时长限制

### 服务器配置
```javascript
// 在API路由中添加文件大小限制
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '100mb', // 调整为合适的大小
    },
  },
}
```

## 扩展功能

### 自定义录制配置
```typescript
const recorder = new VideoRecorder({
  width: 1920,
  height: 1080,
  frameRate: 60,              // 高帧率
  videoBitsPerSecond: 10000000, // 高比特率
  audioBitsPerSecond: 192000   // 高音质
});
```

### 添加水印
可以在 `renderFrame()` 方法中添加水印渲染逻辑。

### 实时预览
可以将录制的Canvas内容实时显示在预览窗口中。

## 技术架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   浏览器录制     │    │    格式转换       │    │   文件下载      │
│                │    │                 │    │                │
│ MediaRecorder   │───▶│ WebM → MP4      │───▶│ 自动下载        │
│ Canvas Capture  │    │ FFmpeg处理      │    │ 多格式支持      │
│ Audio Sync      │    │ 质量优化        │    │                │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 更新日志

### v1.0.0
- ✅ 基础录制功能
- ✅ 浏览器兼容性检测
- ✅ Canvas动画捕获
- ✅ 音频同步录制
- ✅ 自动格式转换
- ✅ AnimationModal集成
- ✅ 测试页面和文档

## 支持和反馈

如有问题或建议，请查看：
1. 测试页面的实时日志
2. 浏览器开发者工具控制台
3. 服务器日志中的FFmpeg输出

---

*该功能基于现代Web标准构建，为教育内容制作提供专业级的视频录制能力。*
