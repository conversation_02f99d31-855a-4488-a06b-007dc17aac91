import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { AudioProcessor } from '@/lib/audioProcessor';

// 定义 Task 接口
interface Task {
  stem_id: number;
  // ... 其他可能的属性
}

/**
 * 获取任务状态
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { executeId: string } }
) {
  try {
    const executeId = params.executeId;

    // 检查是否是直接生成动画的任务
    if (executeId.startsWith('direct_')) {
      // 对于直接生成动画的任务，直接返回完成状态
      const task = await db.getTask(executeId);
      if (task && task.new_response) {
        return NextResponse.json({
          status: 'completed',
          result: task.new_response
        });
      } else {
        return NextResponse.json({
          status: 'completed',
          result: '动画生成完成'
        });
      }
    }

    const apiToken = process.env.COZE_API_TOKEN;
    const workflowId = process.env.COZE_WORKFLOW_ID;

    if (!apiToken || !workflowId) {
      throw new Error('Missing required environment variables');
    }

    // 调用 Coze API 检查状态
    const response = await fetch(
      `https://api.coze.cn/v1/workflows/${workflowId}/run_histories/${executeId}`,
      {
        headers: {
          'Authorization': `Bearer ${apiToken}`,
          'Accept': 'application/json'
        }
      }
    );

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    const responseText = await response.text();
    const responseData = JSON.parse(responseText);
    
    // 获取第一个数据项
    const executionData = responseData.data?.[0];
    if (!executionData) {
      return NextResponse.json({ status: 'pending' });
    }

    // 检查执行状态
    const isCompleted = executionData.execute_status === 'Success';
    
    // 如果完成，更新数据库并处理音频
    if (isCompleted && executionData.output) {
      try {
        // 更新任务响应
        await db.updateTaskResponse(executeId, executionData.output);

        // 获取关联的 stem_id
        const task = await db.getTask(executeId) as Task | null;
        if (task) {
          // 处理音频文件
          await AudioProcessor.processTaskAudio(
            executeId,
            task.stem_id,
            executionData.output
          );
        }
      } catch (error) {
        console.error('Failed to process task completion:', error);
        // 继续返回结果，但记录错误
      }
    }

    return NextResponse.json({
      status: isCompleted ? 'completed' : 'pending',
      result: executionData.output
    });
    
  } catch (error) {
    console.error('Status check error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Failed to check status',
        details: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
} 