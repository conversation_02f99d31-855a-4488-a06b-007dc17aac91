import { NextResponse } from 'next/server';
import { db } from '@/lib/db';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const stemId = searchParams.get('stemId');
    
    if (!stemId) {
      return NextResponse.json({ error: 'Missing stemId parameter' }, { status: 400 });
    }

    console.log('[API] 获取音频时长信息, stemId:', stemId);

    // 通过stem_id获取相关的task，然后获取音频信息
    const tasks = await db.getTasksByStemId(parseInt(stemId));
    
    if (!tasks || tasks.length === 0) {
      return NextResponse.json({ error: 'No tasks found for this stem' }, { status: 404 });
    }

    // 获取最新任务的音频信息
    const latestTask = tasks[tasks.length - 1];
    const audioData = await db.getTaskAudios(latestTask.task_id);

    console.log('[API] 找到音频记录:', audioData.length);

    // 返回音频信息，包含时长
    const audioInfo = audioData.map((audio: any) => ({
      audio_id: audio.audio_id,
      audio_name: audio.audio_name,
      local_path: audio.local_path,
      duration: audio.duration,
      status: audio.status
    }));

    return NextResponse.json(audioInfo);
    
  } catch (error) {
    console.error('[API] 获取音频时长信息失败:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
