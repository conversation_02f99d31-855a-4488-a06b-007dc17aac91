'use client';

import { useEffect, useRef } from 'react';

interface CircleMarkerProps {
  width: number;      
  height: number;     
  duration: number;   
  color?: string;     
  delay?: number;     
}

export default function CircleMarker({
  width,
  height,
  duration = 2,
  color = '#F57D23',  // 使用橙色主题色
  delay = 0
}: CircleMarkerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  console.log('⭕ [圈选] 创建圈选组件:', { width, height, duration, delay });
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 正确处理高分辨率屏幕，确保像素对齐
    const scale = window.devicePixelRatio || 1;

    canvas.width = Math.round(width * scale);
    canvas.height = Math.round(height * scale);
    canvas.style.width = `${width}px`;
    canvas.style.height = `${height}px`;
    ctx.scale(scale, scale);

    // 确保绘制位置精确对齐
    ctx.translate(0.5, 0.5);

    const startTime = performance.now();
    const animationDuration = duration * 1000;
    
    // 椭圆参数 - 增加高度
    const centerX = width / 2;
    const centerY = height / 2;
    const radiusX = (width - 6) / 2; // 减少边距
    const radiusY = Math.max((height - 6) / 2, 12); // 最小半径12px，增加高度

    // 不规则椭圆的控制点偏移
    const irregularityFactor = 0.12; // 稍微减少不规则程度
    
    const animate = (currentTime: number) => {
      const elapsed = Math.max(currentTime - startTime - (delay * 1000), 0);
      const progress = duration === 0 ? 1 : Math.min(elapsed / animationDuration, 1);

      ctx.clearRect(0, 0, canvas.width, canvas.height);

      if (progress > 0) {
        ctx.beginPath();
        ctx.strokeStyle = color;
        ctx.lineWidth = 1.8; // 细化线条
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';

        // 绘制不规则椭圆，留一点缺口
        const totalAngle = Math.PI * 2 * 0.85; // 留15%的缺口
        const currentAngle = totalAngle * progress;

        // 从左上方开始，调整为从左到右的绘制方向（约-120度开始）
        const startAngle = -Math.PI * 0.67;
        
        let isFirstPoint = true;
        const step = 0.05; // 更小的步长使曲线更平滑
        
        for (let angle = 0; angle <= currentAngle; angle += step) {
          const actualAngle = startAngle + angle;
          
          // 添加不规则性
          const irregularX = Math.sin(actualAngle * 3) * irregularityFactor;
          const irregularY = Math.cos(actualAngle * 2.5) * irregularityFactor;
          
          const x = centerX + (radiusX + irregularX * radiusX) * Math.cos(actualAngle);
          const y = centerY + (radiusY + irregularY * radiusY) * Math.sin(actualAngle);
          
          if (isFirstPoint) {
            ctx.moveTo(x, y);
            isFirstPoint = false;
          } else {
            ctx.lineTo(x, y);
          }
        }
        
        ctx.stroke();
      }
      
      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate);
      }
    };
    
    if (delay === 0 && duration === 0) {
      // 立即显示完整状态（已完成标记）
      console.log('⭕ [圈选] 立即显示完整状态（已完成标记）');
      animationRef.current = requestAnimationFrame(animate);
    } else if (delay === 0) {
      // 立即开始动画
      console.log('⭕ [圈选] 立即开始动画');
      animationRef.current = requestAnimationFrame(animate);
    } else {
      console.log(`⭕ [圈选] 延迟 ${delay} 秒后开始动画`);
      setTimeout(() => {
        console.log('⭕ [圈选] 延迟时间到，开始动画');
        animationRef.current = requestAnimationFrame(animate);
      }, delay * 1000);
    }
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [width, height, duration, color, delay]);

  return (
    <canvas
      ref={canvasRef}
      className="absolute pointer-events-none"
      style={{
        width: `${width}px`,
        height: `${height}px`,
        left: '50%',
        top: '50%',
        transform: 'translate(-50%, -50%)'
      }}
    />
  );
}
