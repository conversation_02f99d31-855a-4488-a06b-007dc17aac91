'use client';

import { useEffect, useRef } from 'react';

interface WavyUnderlineProps {
  width: number;      
  duration: number;   
  amplitude?: number; 
  color?: string;     
  phase?: number;
  delay?: number;     
}

export default function WavyUnderline({
  width,
  duration = 1,
  amplitude = 1.5,    // 减小振幅
  color = '#F87171',  // 使用 Tailwind red-400，更柔和的红色
  phase = 0,
  delay = 1
}: WavyUnderlineProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  console.log('🌊 [波浪线] 创建波浪线组件:', { width, duration, delay });
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 正确处理高分辨率屏幕，确保像素对齐
    const scale = window.devicePixelRatio || 1;
    const canvasHeight = amplitude * 2 + 2;

    canvas.width = Math.round(width * scale);
    canvas.height = Math.round(canvasHeight * scale);
    canvas.style.width = `${width}px`;
    canvas.style.height = `${canvasHeight}px`;
    ctx.scale(scale, scale);

    // 确保绘制位置精确对齐
    ctx.translate(0.5, 0.5);

    // 使用固定字符宽度计算频率
    const charWidth = 15; // 固定字符宽度
    const frequency = Math.PI / charWidth;
    const startTime = performance.now();
    const animationDuration = duration * 1000;
    
    const animate = (currentTime: number) => {
      const elapsed = Math.max(currentTime - startTime - (delay * 1000), 0);
      const progress = duration === 0 ? 1 : Math.min(elapsed / animationDuration, 1);

      ctx.clearRect(0, 0, canvas.width, canvas.height);

      ctx.beginPath();
      ctx.strokeStyle = color;
      ctx.lineWidth = scale * 0.7; // 减小线条粗度
      ctx.lineCap = 'round';

      const currentWidth = width * progress;
      const step = 0.5;

      for (let x = 0; x <= currentWidth; x += step) {
        const y = amplitude * Math.sin(x * frequency + phase) + amplitude + 1;
        if (x === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }

      ctx.stroke();

      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate);
      }
    };
    
    if (delay === 0 && duration === 0) {
      // 立即显示完整状态（已完成标记）
      console.log('🌊 [波浪线] 立即显示完整状态（已完成标记）');
      animationRef.current = requestAnimationFrame(animate);
    } else if (delay === 0) {
      // 立即开始动画
      console.log('🌊 [波浪线] 立即开始动画');
      animationRef.current = requestAnimationFrame(animate);
    } else {
      console.log(`🌊 [波浪线] 延迟 ${delay} 秒后开始动画`);
      setTimeout(() => {
        console.log('🌊 [波浪线] 延迟时间到，开始动画');
        animationRef.current = requestAnimationFrame(animate);
      }, delay * 1000);
    }
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [width, duration, amplitude, color, phase, delay]);

  return (
    <canvas
      ref={canvasRef}
      className="absolute left-0 pointer-events-none"
      style={{
        width: `${width}px`,
        height: `${amplitude * 2 + 2}px`
      }}
    />
  );
} 